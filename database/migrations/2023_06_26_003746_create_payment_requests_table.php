<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_requests', function (Blueprint $table) {
            $table->id();
            $table->string('request_id')->nullable();
            $table->string('trxid')->nullable();
            $table->double('amount', 12, 2);
            $table->string('payment_method')->nullable();
            $table->unsignedBigInteger('merchant_id')->index()->nullable();
            $table->string('reference', 20)->nullable()->comment('it will invoice_id of merchant"s order');
            $table->enum('currency', ['BDT', 'USD', 'EURO'])->default('BDT');
            $table->text('callback_url')->nullable();
            $table->string('sim_id')->nullable();
            $table->text('cust_name')->nullable();
            $table->text('cust_phone')->nullable();
            $table->text('cust_address')->nullable();
            $table->text('checkout_items')->nullable();
            $table->text('note')->nullable();
            $table->text('ext_field_1')->nullable();
            $table->text('ext_field_2')->nullable();
            $table->timestamp('issue_time')->nullable()->comment('This will be filled by system');
            $table->integer('agent')->nullable();
            $table->integer('dso')->nullable();
            $table->integer('partner')->nullable();
            $table->integer('modem_id')->index()->nullable();
            $table->string('device_id')->index()->nullable();
            $table->string('customer_id')->index()->nullable();
            $table->string('ip')->nullable();
            $table->string('user_agent')->nullable();
            $table->string('accepted_by')->nullable();
            $table->string('status')->default(0)->comment('it may be 1=completed, 0=pending, 2=accepted, 3=rejected etc');
            $table->foreign('merchant_id')
                ->references('id')
                ->on('merchants')
                ->onUpdate('cascade')
                ->onDelete('restrict');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_requests');
    }
};
