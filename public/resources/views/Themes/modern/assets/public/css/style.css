.tabs nav ul {
    position: relative;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: flex;
    margin: 0 auto;
    padding: 0;
    max-width: 1200px;
    list-style: none;
    -ms-box-orient: horizontal;
    -ms-box-pack: center;
    -webkit-flex-flow: row wrap;
    -moz-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -webkit-justify-content: center;
    -moz-justify-content: center;
    -ms-justify-content: center;
    justify-content: center;
}

.tabs nav ul li {
    position: relative;
    z-index: 1;
    display: block;
    margin: 0;
    text-align: center;
    -webkit-flex: 1;
    -moz-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.tabs nav a {
    position: relative;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 2.5;
}

.tabs nav a span {
    vertical-align: middle;
    font-size: 0.75em;
}

.tabs nav li.tab-current a {
    /* color: #74777b; */
    text-decoration: none;
    outline: none;
    font-weight: bold;
}

.tabs nav a:focus {
    outline: none;
}

/* Content */
.content-wrap {
    position: relative;
}

.content-wrap section {
    display: none;
    margin: 0 auto;
    padding: 25px;
    max-width: 1200px;
    text-align: center;
}

.content-wrap section.content-current {
    display: block;
}

.content-wrap section p {
    margin: 0;
    padding: 0.75em 0;
    color: rgba(40, 44, 42, 0.05);
    font-weight: 900;
    font-size: 4em;
    line-height: 1;
}

/*****************************/
/* Shape */
/*****************************/
.tabs-style-shape {
    max-width: 1200px;
}

.tabs-style-shape nav ul li {
    margin: 0 3em;
}

.tabs-style-shape nav ul li:first-child {
    margin-left: 0;
}

.tabs-style-shape nav ul li.tab-current {
    z-index: 100;
}

.tabs-style-shape nav li a {
    overflow: visible;
    margin: 0 -3em 0 0;
    padding: 0;
    color: #fff;
    font-weight: 500;
    text-decoration: none;
    outline: none;
}

.tabs-style-shape nav li:first-child a span {
    padding-left: 2em;
    border-radius: 12px 0 0 0;
}

.tabs-style-shape nav li:last-child a span {
    padding-right: 2em;
    border-radius: 0 12px 0 0;
    font-weight: bold;
}

.tabs-style-shape nav li a svg {
    position: absolute;
    left: 100%;
    margin: 0;
    width: 3em;
    height: 100%;
    fill: #bdc2c9;
}

.tabs-style-shape nav li a svg:nth-child(2),
.tabs-style-shape nav li:last-child a svg {
    right: 100%;
    left: auto;
    -webkit-transform: scale3d(-1, 1, 1);
    transform: scale3d(-1, 1, 1);
}

.tabs-style-shape nav li a span {
    display: block;
    overflow: hidden;
    padding: 0.65em 0;
    /* background-color: #bdc2c9; */
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tabss {
    background-color: #bdc2c9;
}

/* .tabs-style-shape nav li a:hover span {
    background-color: #2CC185;
  } */

/* .tabs-style-shape nav li a:hover svg {
    fill: #2CC185;
  } */

/* Make only shape clickable */
.tabs-style-shape nav li a svg {
    pointer-events: none;
}

.tabs-style-shape nav li a svg use {
    pointer-events: auto;
}

.tabs-style-shape nav li.tab-current a span,
.tabs-style-shape nav li.tab-current a svg {
    -webkit-transition: none;
    transition: none;
}

/* .tabs-style-shape nav li.tab-current a span {
    background: #635bff;
  } */

.tab-active {
    background: #635bff !important;
}

/* .tabs-style-shape nav li.tab-current a svg {
    fill: #635bff;
  } */

.active-svg {
    fill: #635bff !important;
}

.tabs-style-shape .content-wrap {
    background: #fff;
    /* height: 348px; */
    border: 1px solid rgb(221, 221, 221);
}

@media screen and (max-width: 58em) {
    .tabs-style-shape nav ul {
        display: block;
        padding-top: 1.5em;
    }

    .tabs-style-shape nav ul li {
        display: block;
        margin: -1.25em 0 0;
        -webkit-flex: none;
        flex: none;
    }

    .tabs-style-shape nav ul li a {
        margin: 0;
    }

    .tabs-style-shape nav ul li svg {
        display: none;
    }

    .tabs-style-shape nav ul li a span {
        padding: 1.25em 0 2em !important;
        border-radius: 30px 30px 0 0 !important;
        box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.1);
        line-height: 1;
    }

    .tabs-style-shape nav ul li:last-child a span {
        padding: 1.25em 0 !important;
    }

    .tabs-style-shape nav ul li.tab-current {
        z-index: 1;
    }
}

.custom-label {
    margin-top: -74px;
    display: block;
    float: left;
    padding: 10px;
    font-size: 14px;
    position: absolute;
}
.custom-height {
    height: calc(1.5em + 1.75rem + 5px) !important;
    padding-top: 12px;
    font-size: 22px !important;
    position: relative;
}

.StepProgress {
    position: relative;
    padding-left: 45px;
    list-style: none;
}
.StepProgress::before {
    display: inline-block;
    content: "";
    position: absolute;
    top: -13px;
    left: 15px;
    width: 10px;
    height: 140px;
    border-left: 1px solid #ccc;
}
.StepProgress-item {
    position: relative;
    counter-increment: list;
    font-size: 11px;
    font-weight: bold;
    text-align: left;
    color: #585858;
    font-weight: 400;
    line-height: 24px;
}
.border-dashed {
    border-bottom: 1px dashed #002ab6;
}

.StepProgress-item:not(:last-child) {
    /* padding-bottom: 20px; */
}
.StepProgress-item::before {
    display: inline-block;
    content: "";
    position: absolute;
    left: -30px;
    height: 100%;
    width: 10px;
}
.StepProgress-item::after {
    content: "";
    display: inline-block;
    position: absolute;
    top: 8px;
    left: -34px;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background-color: #fff;
}

.StepProgress-item.is-done::after {
    background-color: rgb(219, 221, 219);
}

.StepProgress strong {
    display: block;
}

/* exchange button */

.btn-swich {
    background-color: #b7b7b7;
    padding: 0 7px 0 7px;
    border-radius: 7px;
    color: white;
}

.btn-defaults {
    padding: 0 5px 0 5px;
    background-color: unset !important;
}

.btns {
    border: unset !important;
}

.switch-box {
    background-color: #f3f3f3;
    padding: 5px;
    border-radius: 7px;
    float: right;
    margin-top: 7px;
}
.custom-m {
    margin-top: 12px;
    margin-bottom: 12px;
}
.Mt-35 {
    margin-top: 35px;
}

.fiat-buttons-swap__arrow-buy {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    margin-left: 3px;
}

.layered-currency-switcher {
    width: auto;
    float: right;
    padding: 0 0 0 50px;
    margin: 0px;
}

.layered-currency-switcher li {
    display: block;
    float: left;
    font-size: 15px;
    margin: 0px;
}

.doubly,
.money {
    position: relative;
    font-weight: inherit !important;
    font-size: inherit !important;
    text-decoration: inherit !important;
}

.doubly-message {
    margin: 5px 0;
}

.doubly-wrapper {
    float: right;
}

.doubly-float {
    position: fixed;
    bottom: 10px;
    left: 10px;
    right: auto;
    z-index: 100000;
}

select.currency-switcher {
    margin: 0px;
    position: relative;
    top: auto;
}

select.currency-switcher {
    display: none;
}

.exchange-btn{
    font-size: 16px;
    font-weight: 700;
}

.exchagne-head{
    background-color: #635BFF;
    margin:0 !important;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}

.exchane-heads{
    /* background-color: #635BFF; */
    margin:0 !important;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}

.font-weights{
    font-weight: 700;
    color: #6d6d6d;
    font-size: 32px;
}

.p-title{
    font-weight: 700;
    color: #6D6D6D;
}
.pay-font
{
    font-size: 14px !important;
}

.crypto-padding{
    padding: 1.5rem!important;
    padding-top: unset !important;
}

.stripe-image{
    width: 100px;
    border-radius: 5px
}

.stepper .line {
    width: 2px;
    background-color: lightgrey !important;
  }
  .stepper .lead {
    font-size: 1.1rem;
  }

  .circle-box{
    width:36px !important;
    height: 36px;
  }

  .circle-pad{
    padding-left: 10px;
    padding-top: 10px;
  }

  .circle-border{
    border:2px solid #bdbdbd;
  }

  .line-h{
      height: 20px;
  }


  .tic-bac{
    background-color: #635BFF
  }


  .tic-gray{
    background-color: #cacad3
  }



  .swal-icon--success {
    border-color: #a5dc86
  }

  .swal-icon--success:after, .swal-icon--success:before {
    content: "";
    border-radius: 50%;
    position: absolute;
    width: 60px;
    height: 120px;
    background: #fff;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
  }

  .swal-icon--success:before {
    border-radius: 120px 0 0 120px;
    top: -7px;
    left: -33px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-transform-origin: 60px 60px;
    transform-origin: 60px 60px
  }

  .swal-icon--success:after {
    border-radius: 0 120px 120px 0;
    top: -11px;
    left: 30px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-transform-origin: 0 60px;
    transform-origin: 0 60px;
    -webkit-animation: rotatePlaceholder 4.25s ease-in;
    animation: rotatePlaceholder 4.25s ease-in
  }

  .swal-icon--success__ring {
    width: 66px;
    height: 66px;
    border: 4px solid hsla(98deg 55% 69% / 50%);
    border-radius: 50%;
    box-sizing: content-box;
    position: absolute;
    left: 1px;
    top: 8px;
    z-index: 2;
  }

  .swal-icon--success__hide-corners {
    width: 5px;
    height: 90px;
    background-color: #fff;
    padding: 1px;
    position: absolute;
    left: 28px;
    top: 8px;
    z-index: 1;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
  }

  .swal-icon--success__line {
    height: 4px;
    background-color: #a5dc86;
    display: block;
    border-radius: 2px;
    position: absolute;
    z-index: 2;
  }

  .swal-icon--success__line--tip {
    width: 16px;
    left: 21px;
    top: 49px;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-animation: animateSuccessTip .75s;
    animation: animateSuccessTip .75s
  }

  .swal-icon--success__line--long {
    width: 35px;
    right: 17px;
    top: 43px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-animation: animateSuccessLong .75s;
    animation: animateSuccessLong .75s
  }

  .swal-icon {
    width: 80px;
    height: 80px;
    border-width: 4px;
    border-style: solid;
    border-radius: 50%;
    padding: 0;
    position: relative;
    box-sizing: content-box;
    margin: 20px auto
  }

  .swal-icon:first-child {
    margin-top: -23px
  }

  @keyframes animateSuccessLong {
    0% {
      width: 0;
      right: 46px;
      top: 54px
    }
    65% {
      width: 0;
      right: 46px;
      top: 54px
    }
    84% {
      width: 55px;
      right: 0;
      top: 35px
    }
    to {
      width: 47px;
      right: 8px;
      top: 38px
    }
  }

  @keyframes rotatePlaceholder {
    0% {
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg)
    }
    5% {
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg)
    }
    12% {
      -webkit-transform: rotate(-405deg);
      transform: rotate(-405deg)
    }
    to {
      -webkit-transform: rotate(-405deg);
      transform: rotate(-405deg)
    }
  }

  @keyframes animateSuccessTip {
    0% {
      width: 0;
      left: 1px;
      top: 19px
    }
    54% {
      width: 0;
      left: 1px;
      top: 19px
    }
    70% {
      width: 50px;
      left: -8px;
      top: 37px
    }
    84% {
      width: 17px;
      left: 21px;
      top: 48px
    }
    to {
      width: 25px;
      left: 14px;
      top: 45px
    }
  }

  /* create by styling */
  .creditos {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ddd;
  }

  .logo-sweetalert {
    background: url(https://sweetalert.js.org/assets/images/logo.svg) center no-repeat;
    background-size: contain;
    width: 70px;
    height: 40px;
    margin-left: .5em;
    color: transparent;
  }
.log-img{
    width: 100px;
}