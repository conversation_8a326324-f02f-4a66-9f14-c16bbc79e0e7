"use strict";$("#switch").on("click",function(){$("body").hasClass("dark")?($("body").removeClass("dark"),$(this).find("img").toggle(),$("#switch").removeClass("switched"),localStorage.setItem("theme","light")):($("body").addClass("dark"),$(this).find("img").toggle(),$("#switch").addClass("switched"),localStorage.setItem("theme","dark"))});var theme=localStorage.getItem("theme");"dark"==theme?($("body").addClass("dark"),$("img.sun").removeClass("img-none"),$("img.moon").addClass("img-none"),$("#switch").addClass("switched")):($("body").removeClass("dark"),$("img.moon").removeClass("img-none"),$("img.sun").addClass("img-none"),$("#switch").removeClass("switched")),function(){var a=function(){$(".owl-carousel1").owlCarousel({loop:!0,center:!0,margin:0,responsiveClass:!0,nav:!1,responsive:{0:{items:1,nav:!1},680:{items:2,nav:!0,loop:!0},991:{items:3,nav:!0}}})};(function(){a()})(jQuery)}(),$(document).ready(function(){$(window).scroll(function(){var a=$(window).scrollTop();95<a?$(".bg-white").addClass("shadow-sm"):$(".start-header").removeClass("shadow-sm")})});
