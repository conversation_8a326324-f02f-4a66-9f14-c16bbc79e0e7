"use strict";let hasPhoneError=!1,hasEmailError=!1;function enableDisableButton(){hasPhoneError||hasEmailError?$("form").find("button[type='submit']").prop("disabled",!0):$("form").find("button[type='submit']").prop("disabled",!1)}function updatePhoneInfo(){let promiseObj;return new Promise((function(resolve,reject){$("#defaultCountry").val($("#phone").intlTelInput("getSelectedCountryData").iso2),$("#carrierCode").val($("#phone").intlTelInput("getSelectedCountryData").dialCode),""!=$("#phone").val&&$("#formattedPhone").val($("#phone").intlTelInput("getNumber").replace(/-|\s/g,"")),resolve()}))}function checkDuplicatePhoneNumber(){$.ajax({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")},method:"POST",url:SITE_URL+"/register/duplicate-phone-number-check",dataType:"json",cache:!1,data:{phone:$.trim($("#phone").val()),carrierCode:$.trim($("#phone").intlTelInput("getSelectedCountryData").dialCode)}}).done((function(response){response.status?($("#duplicate-phone-error").show().addClass("error").html(response.fail),hasPhoneError=!0,enableDisableButton()):($("#duplicate-phone-error").html(""),hasPhoneError=!1,enableDisableButton())}))}function validateInternaltionalPhoneNumber(){let promiseObj;return new Promise((function(resolve,reject){let resolveStatus=!1;""!==$.trim($("#phone").val())?$("#phone").intlTelInput("isValidNumber")&&isValidPhoneNumber($.trim($("#phone").val()))?(resolveStatus=!0,$("#tel-error").html(""),hasPhoneError=!1,enableDisableButton()):($("#duplicate-phone-error").html(""),$("#tel-error").addClass("error").html(validPhoneNumberText),hasPhoneError=!0,enableDisableButton()):($("#tel-error").html(""),hasPhoneError=!1,enableDisableButton()),resolve(resolveStatus)}))}function phoneValidityCheck(){updatePhoneInfo().then(()=>{validateInternaltionalPhoneNumber().then(status=>{status&&checkDuplicatePhoneNumber()})})}$("#register_form").on("submit",(function(){$("#users_create").attr("disabled",!0),$(".spinner").removeClass("d-none"),$("#users_create_text").text(signingUpText)})),$("#phone").intlTelInput({separateDialCode:!0,nationalMode:!0,preferredCountries:[countryShortCode],autoPlaceholder:"polite",placeholderNumberType:"MOBILE",utilsScript:utilsJsScript}),$("#phone").on("countrychange",(function(){phoneValidityCheck()})),$("#phone").on("blur",(function(){phoneValidityCheck()})),$(document).ready((function(){$("#email").on("input",(function(){var email=$("#email").val();$.ajax({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")},method:"POST",url:SITE_URL+"/user-registration-check-email",dataType:"json",data:{email:email}}).done((function(response){function validateEmail(email){var re;return/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(email)}function emptyEmail(){0===email.length&&($("#email_error").html(""),$("#email_ok").html(""))}response.status?(emptyEmail(),validateEmail(email)?($("#email_error").addClass("error").html(response.fail),$("#email_ok").html(""),hasEmailError=!0,enableDisableButton()):$("#email_error").html("")):(emptyEmail(),validateEmail(email)?$("#email_error").html(""):$("#email_ok").html(""),hasEmailError=!1,enableDisableButton())}))}))}));