/*! MenuSpy v1.3.0 (Jan 31 2018) - http://leocs.me/menuspy/ - Copyright (c) 2018 <PERSON>; MIT License */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.MenuSpy=e()}(this,function(){"use strict";var t=function(t,e){for(var s in e)e.hasOwnProperty(s)&&(t[s]=e[s]);return t},e=function(t){var e=t.getBoundingClientRect();return{top:e.top+window.pageYOffset,left:e.left+window.pageXOffset}},s=function(){return window.pageYOffset||document.documentElement.scrollTop},n=function(t,e){if(t.classList)t.classList.add(e);else{var s=t.className.split(" ");-1===s.indexOf(e)&&s.push(e),t.className=s.join(" ")}},i=function(t,e){t.classList?t.classList.remove(e):t.className=t.className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ")},o=function(t,e){var s=null;return function(){var n=arguments,i=this;s||(s=setTimeout(function(){return s=0,t.apply(i,n)},e))}},l=function(e,n){var i=this;if(e){this.element="string"==typeof e?document.querySelector(e):e,this.options=t({menuItemSelector:'a[href^="#"]',activeClass:"active",threshold:15,enableLocationHash:!0,hashTimeout:600,callback:null},n),this.assignValues(),this.debouncedAssignValuesFn=o(function(){return i.assignValues()}),window.addEventListener("resize",this.debouncedAssignValuesFn),this.debouncedHashFn=o(function(){var t=i.lastInViewElm?"#"+i.lastInViewElm.id:"#";if(history.replaceState)history.replaceState(null,null,t);else{var e=s();window.location.hash=t,window.scrollTo(0,e)}},this.options.hashTimeout),this.cacheItems(),this.scrollFn()}};return l.prototype.assignValues=function(){this.currScrollTop=0,this.lastInViewElm=null,this.menuHeight=this.element.offsetHeight+this.options.threshold,this.menuItems=[].slice.call(this.element.querySelectorAll(this.options.menuItemSelector)),this.raf=null},l.prototype.cacheItems=function(){this.scrollItems=this.menuItems.map(function(t){var s=t.dataset.target?document.querySelector(t.dataset.target):document.getElementById(t.hash.slice(1));return!!s&&{elm:t,target:s,offset:Math.floor(e(s).top)}}),this.scrollItems=this.scrollItems.filter(Boolean).sort(function(t,e){return t.offset-e.offset})},l.prototype.tick=function(){var t=this.currScrollTop+this.menuHeight,e=this.scrollItems.filter(function(e){return e.offset<t});this.activateItem(e.pop())},l.prototype.activateItem=function(t){var e=this,s=this.options,o=s.activeClass,l=s.callback;if(!t)return this.scrollItems.forEach(function(t){return i(t.elm.parentNode,o)}),this.lastInViewElm=null,void(this.options.enableLocationHash&&this.debouncedHashFn());this.lastInViewElm!==t.target&&(this.lastInViewElm=t.target,this.scrollItems.forEach(function(s){i(s.elm.parentNode,o),s.target===t.target&&(n(s.elm.parentNode,o),"function"==typeof l&&l.call(e,s),e.options.enableLocationHash&&e.debouncedHashFn())}))},l.prototype.scrollFn=function(){var t=s();this.currScrollTop!==t&&(this.currScrollTop=t,this.tick()),this.raf=window.requestAnimationFrame(this.scrollFn.bind(this))},l.prototype.destroy=function(){this.raf&&window.cancelAnimationFrame(this.raf),window.removeEventListener("resize",this.debouncedAssignValuesFn)},l});
