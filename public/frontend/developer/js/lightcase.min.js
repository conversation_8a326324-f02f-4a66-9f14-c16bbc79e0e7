!function(r){"use strict";var f={cache:{},support:{},objects:{},init:function(e){return this.each(function(){r(this).unbind("click.lightcase").bind("click.lightcase",function(t){t.preventDefault(),r(this).lightcase("start",e)})})},start:function(t){f.origin=lightcase.origin=this,f.settings=lightcase.settings=r.extend(!0,{idPrefix:"lightcase-",classPrefix:"lightcase-",attrPrefix:"lc-",transition:"elastic",transitionOpen:null,transitionClose:null,transitionIn:null,transitionOut:null,cssTransitions:!0,speedIn:250,speedOut:250,width:null,height:null,maxWidth:800,maxHeight:500,forceWidth:!1,forceHeight:!1,liveResize:!0,fullScreenModeForMobile:!0,mobileMatchExpression:/(iphone|ipod|ipad|android|blackberry|symbian)/,disableShrink:!1,fixedRatio:!0,shrinkFactor:.75,overlayOpacity:.9,slideshow:!1,slideshowAutoStart:!0,breakBeforeShow:!1,timeout:5e3,swipe:!0,useKeys:!0,useCategories:!0,useAsCollection:!1,navigateEndless:!0,closeOnOverlayClick:!0,title:null,caption:null,showTitle:!0,showCaption:!0,showSequenceInfo:!0,inline:{width:"auto",height:"auto"},ajax:{width:"auto",height:"auto",type:"get",dataType:"html",data:{}},iframe:{width:800,height:500,frameborder:0},flash:{width:400,height:205,wmode:"transparent"},video:{width:400,height:225,poster:"",preload:"auto",controls:!0,autobuffer:!0,autoplay:!0,loop:!1},attr:"data-rel",href:null,type:null,typeMapping:{image:"jpg,jpeg,gif,png,bmp",flash:"swf",video:"mp4,mov,ogv,ogg,webm",iframe:"html,php",ajax:"json,txt",inline:"#"},errorMessage:function(){return'<p class="'+f.settings.classPrefix+'error">'+f.settings.labels.errorMessage+"</p>"},labels:{errorMessage:"Source could not be found...","sequenceInfo.of":" of ",close:"Close","navigator.prev":"Prev","navigator.next":"Next","navigator.play":"Play","navigator.pause":"Pause"},markup:function(){f.objects.body.append(f.objects.overlay=r('<div id="'+f.settings.idPrefix+'overlay"></div>'),f.objects.loading=r('<div id="'+f.settings.idPrefix+'loading" class="'+f.settings.classPrefix+'icon-spin"></div>'),f.objects.case=r('<div id="'+f.settings.idPrefix+'case" aria-hidden="true" role="dialog"></div>')),f.objects.case.after(f.objects.close=r('<a href="#" class="'+f.settings.classPrefix+'icon-close"><span>'+f.settings.labels.close+"</span></a>"),f.objects.nav=r('<div id="'+f.settings.idPrefix+'nav"></div>')),f.objects.nav.append(f.objects.prev=r('<a href="#" class="'+f.settings.classPrefix+'icon-prev"><span>'+f.settings.labels["navigator.prev"]+"</span></a>").hide(),f.objects.next=r('<a href="#" class="'+f.settings.classPrefix+'icon-next"><span>'+f.settings.labels["navigator.next"]+"</span></a>").hide(),f.objects.play=r('<a href="#" class="'+f.settings.classPrefix+'icon-play"><span>'+f.settings.labels["navigator.play"]+"</span></a>").hide(),f.objects.pause=r('<a href="#" class="'+f.settings.classPrefix+'icon-pause"><span>'+f.settings.labels["navigator.pause"]+"</span></a>").hide()),f.objects.case.append(f.objects.content=r('<div id="'+f.settings.idPrefix+'content"></div>'),f.objects.info=r('<div id="'+f.settings.idPrefix+'info"></div>')),f.objects.content.append(f.objects.contentInner=r('<div class="'+f.settings.classPrefix+'contentInner"></div>')),f.objects.info.append(f.objects.sequenceInfo=r('<div id="'+f.settings.idPrefix+'sequenceInfo"></div>'),f.objects.title=r('<h4 id="'+f.settings.idPrefix+'title"></h4>'),f.objects.caption=r('<p id="'+f.settings.idPrefix+'caption"></p>'))},onInit:{},onStart:{},onBeforeCalculateDimensions:{},onAfterCalculateDimensions:{},onBeforeShow:{},onFinish:{},onResize:{},onClose:{},onCleanup:{}},t,f.origin.data?f.origin.data("lc-options"):{}),f.objects.document=r("html"),f.objects.body=r("body"),f._callHooks(f.settings.onInit),f.objectData=f._setObjectData(this),f._addElements(),f._open(),f.dimensions=f.getViewportDimensions()},get:function(t){return f.objects[t]},getObjectData:function(){return f.objectData},_setObjectData:function(t){var e=r(t),i={this:r(t),title:f.settings.title||e.attr(f._prefixAttributeName("title"))||e.attr("title"),caption:f.settings.caption||e.attr(f._prefixAttributeName("caption"))||e.children("img").attr("alt"),url:f._determineUrl(),requestType:f.settings.ajax.type,requestData:f.settings.ajax.data,requestDataType:f.settings.ajax.dataType,rel:e.attr(f._determineAttributeSelector()),type:f.settings.type||f._verifyDataType(f._determineUrl()),isPartOfSequence:f.settings.useAsCollection||f._isPartOfSequence(e.attr(f.settings.attr),":"),isPartOfSequenceWithSlideshow:f._isPartOfSequence(e.attr(f.settings.attr),":slideshow"),currentIndex:r(f._determineAttributeSelector()).index(e),sequenceLength:r(f._determineAttributeSelector()).length};return i.sequenceInfo=i.currentIndex+1+f.settings.labels["sequenceInfo.of"]+i.sequenceLength,i.prevIndex=i.currentIndex-1,i.nextIndex=i.currentIndex+1,i},_prefixAttributeName:function(t){return"data-"+f.settings.attrPrefix+t},_determineLinkTarget:function(){return f.settings.href||r(f.origin).attr(f._prefixAttributeName("href"))||r(f.origin).attr("href")},_determineAttributeSelector:function(){var t=r(f.origin),i="";if(void 0!==f.cache.selector)i=f.cache.selector;else if(!0===f.settings.useCategories&&t.attr(f._prefixAttributeName("categories"))){var e=t.attr(f._prefixAttributeName("categories")).split(" ");r.each(e,function(t,e){0<t&&(i+=","),i+="["+f._prefixAttributeName("categories")+'~="'+e+'"]'})}else i="["+f.settings.attr+'="'+t.attr(f.settings.attr)+'"]';return f.cache.selector=i},_determineUrl:function(){var n,t=f._verifyDataUrl(f._determineLinkTarget()),a=0,o=0,c="";return r.each(t,function(t,e){switch(f._verifyDataType(e.url)){case"video":var i=document.createElement("video"),s=f._verifyDataType(e.url)+"/"+f._getFileUrlSuffix(e.url);"probably"!==c&&c!==i.canPlayType(s)&&""!==i.canPlayType(s)&&(c=i.canPlayType(s),n=e.url);break;default:f._devicePixelRatio()>=e.density&&e.density>=o&&f._matchMedia()("screen and (min-width:"+e.width+"px)").matches&&e.width>=a&&(a=e.width,o=e.density,n=e.url)}}),n},_normalizeUrl:function(t){var c=/^\d+$/;return t.split(",").map(function(t){var o={width:0,density:0};return t.trim().split(/\s+/).forEach(function(t,e){if(0===e)return o.url=t;var i=t.substring(0,t.length-1),s=t[t.length-1],n=parseInt(i,10),a=parseFloat(i);"w"===s&&c.test(i)?o.width=n:"h"===s&&c.test(i)?o.height=n:"x"!==s||isNaN(a)||(o.density=a)}),o})},_isPartOfSequence:function(t,e){var i=r("["+f.settings.attr+'="'+t+'"]');return new RegExp(e).test(t)&&1<i.length},isSlideshowEnabled:function(){return f.objectData.isPartOfSequence&&(!0===f.settings.slideshow||!0===f.objectData.isPartOfSequenceWithSlideshow)},_loadContent:function(){f.cache.originalObject&&f._restoreObject(),f._createObject()},_createObject:function(){var i;switch(f.objectData.type){case"image":(i=r(new Image)).attr({src:f.objectData.url,alt:f.objectData.title});break;case"inline":(i=r('<div class="'+f.settings.classPrefix+'inlineWrap"></div>')).html(f._cloneObject(r(f.objectData.url))),r.each(f.settings.inline,function(t,e){i.attr(f._prefixAttributeName(t),e)});break;case"ajax":i=r('<div class="'+f.settings.classPrefix+'inlineWrap"></div>'),r.each(f.settings.ajax,function(t,e){"data"!==t&&i.attr(f._prefixAttributeName(t),e)});break;case"flash":i=r('<embed src="'+f.objectData.url+'" type="application/x-shockwave-flash"></embed>'),r.each(f.settings.flash,function(t,e){i.attr(t,e)});break;case"video":(i=r("<video></video>")).attr("src",f.objectData.url),r.each(f.settings.video,function(t,e){i.attr(t,e)});break;default:(i=r("<iframe></iframe>")).attr({src:f.objectData.url}),r.each(f.settings.iframe,function(t,e){i.attr(t,e)})}f._addObject(i),f._loadObject(i)},_addObject:function(t){f.objects.contentInner.html(t),f._loading("start"),f._callHooks(f.settings.onStart),!0===f.settings.showSequenceInfo&&f.objectData.isPartOfSequence?(f.objects.sequenceInfo.html(f.objectData.sequenceInfo),f.objects.sequenceInfo.show()):(f.objects.sequenceInfo.empty(),f.objects.sequenceInfo.hide()),!0===f.settings.showTitle&&void 0!==f.objectData.title&&""!==f.objectData.title?(f.objects.title.html(f.objectData.title),f.objects.title.show()):(f.objects.title.empty(),f.objects.title.hide()),!0===f.settings.showCaption&&void 0!==f.objectData.caption&&""!==f.objectData.caption?(f.objects.caption.html(f.objectData.caption),f.objects.caption.show()):(f.objects.caption.empty(),f.objects.caption.hide())},_loadObject:function(s){switch(f.objectData.type){case"inline":r(f.objectData.url)?f._showContent(s):f.error();break;case"ajax":r.ajax(r.extend({},f.settings.ajax,{url:f.objectData.url,type:f.objectData.requestType,dataType:f.objectData.requestDataType,data:f.objectData.requestData,success:function(t,e,i){i.getResponseHeader("X-Ajax-Location")?(f.objectData.url=i.getResponseHeader("X-Ajax-Location"),f._loadObject(s)):("json"===f.objectData.requestDataType?f.objectData.data=t:s.html(t),f._showContent(s))},error:function(t,e,i){f.error()}}));break;case"flash":f._showContent(s);break;case"video":"function"==typeof s.get(0).canPlayType||0===f.objects.case.find("video").length?f._showContent(s):f.error();break;default:f.objectData.url?(s.on("load",function(){f._showContent(s)}),s.on("error",function(){f.error()})):f.error()}},error:function(){f.objectData.type="error";var t=r('<div class="'+f.settings.classPrefix+'inlineWrap"></div>');t.html(f.settings.errorMessage),f.objects.contentInner.html(t),f._showContent(f.objects.contentInner)},_calculateDimensions:function(t){if(f._cleanupDimensions(),t){var e={ratio:1,objectWidth:t.attr("width")?t.attr("width"):t.attr(f._prefixAttributeName("width")),objectHeight:t.attr("height")?t.attr("height"):t.attr(f._prefixAttributeName("height"))};if(!f.settings.disableShrink)switch(e.maxWidth=parseInt(f.dimensions.windowWidth*f.settings.shrinkFactor),e.maxHeight=parseInt(f.dimensions.windowHeight*f.settings.shrinkFactor),e.maxWidth>f.settings.maxWidth&&(e.maxWidth=f.settings.maxWidth),e.maxHeight>f.settings.maxHeight&&(e.maxHeight=f.settings.maxHeight),e.differenceWidthAsPercent=parseInt(100/e.maxWidth*e.objectWidth),e.differenceHeightAsPercent=parseInt(100/e.maxHeight*e.objectHeight),f.objectData.type){case"image":case"flash":case"video":case"iframe":case"ajax":case"inline":if("image"===f.objectData.type||!0===f.settings.fixedRatio){100<e.differenceWidthAsPercent&&e.differenceWidthAsPercent>e.differenceHeightAsPercent&&(e.objectWidth=e.maxWidth,e.objectHeight=parseInt(e.objectHeight/e.differenceWidthAsPercent*100)),100<e.differenceHeightAsPercent&&e.differenceHeightAsPercent>e.differenceWidthAsPercent&&(e.objectWidth=parseInt(e.objectWidth/e.differenceHeightAsPercent*100),e.objectHeight=e.maxHeight),100<e.differenceHeightAsPercent&&e.differenceWidthAsPercent<e.differenceHeightAsPercent&&(e.objectWidth=parseInt(e.maxWidth/e.differenceHeightAsPercent*e.differenceWidthAsPercent),e.objectHeight=e.maxHeight);break}case"error":!isNaN(e.objectWidth)&&e.objectWidth>e.maxWidth&&(e.objectWidth=e.maxWidth);break;default:(isNaN(e.objectWidth)||e.objectWidth>e.maxWidth)&&!f.settings.forceWidth&&(e.objectWidth=e.maxWidth),(isNaN(e.objectHeight)&&"auto"!==e.objectHeight||e.objectHeight>e.maxHeight)&&!f.settings.forceHeight&&(e.objectHeight=e.maxHeight)}if(f.settings.forceWidth){try{e.objectWidth=f.settings[f.objectData.type].width}catch(t){e.objectWidth=f.settings.width||e.objectWidth}e.maxWidth=null}if(t.attr(f._prefixAttributeName("max-width"))&&(e.maxWidth=t.attr(f._prefixAttributeName("max-width"))),f.settings.forceHeight){try{e.objectHeight=f.settings[f.objectData.type].height}catch(t){e.objectHeight=f.settings.height||e.objectHeight}e.maxHeight=null}t.attr(f._prefixAttributeName("max-height"))&&(e.maxHeight=t.attr(f._prefixAttributeName("max-height"))),f._adjustDimensions(t,e)}},_adjustDimensions:function(t,e){t.css({width:e.objectWidth,height:e.objectHeight,"max-width":e.maxWidth,"max-height":e.maxHeight}),f.objects.contentInner.css({width:t.outerWidth(),height:t.outerHeight(),"max-width":"100%"}),f.objects.case.css({width:f.objects.contentInner.outerWidth(),"max-width":"100%"}),f.objects.case.css({"margin-top":parseInt(-f.objects.case.outerHeight()/2),"margin-left":parseInt(-f.objects.case.outerWidth()/2)})},_loading:function(t){"start"===t?(f.objects.case.addClass(f.settings.classPrefix+"loading"),f.objects.loading.show()):"end"===t&&(f.objects.case.removeClass(f.settings.classPrefix+"loading"),f.objects.loading.hide())},getViewportDimensions:function(){return{windowWidth:r(window).innerWidth(),windowHeight:r(window).innerHeight()}},_verifyDataUrl:function(t){return!(!t||void 0===t||""===t)&&(-1<t.indexOf("#")&&(t="#"+(t=t.split("#"))[t.length-1]),f._normalizeUrl(t.toString()))},_getFileUrlSuffix:function(t){return/(?:\.([^.]+))?$/.exec(t.toLowerCase())[1]},_verifyDataType:function(t){var e=f.settings.typeMapping;if(!t)return!1;for(var i in e)if(e.hasOwnProperty(i))for(var s=e[i].split(","),n=0;n<s.length;n++){var a=s[n].toLowerCase(),o=new RegExp(".("+a+")$","i"),c=t.toLowerCase().split("?")[0].substr(-5);if(!0===o.test(c)||"inline"===i&&-1<t.indexOf(a))return i}return"iframe"},_addElements:function(){void 0!==f.objects.case&&r("#"+f.objects.case.attr("id")).length||f.settings.markup()},_showContent:function(t){f.objects.document.attr(f._prefixAttributeName("type"),f.objectData.type),f.cache.object=t,f._callHooks(f.settings.onBeforeShow),f.settings.breakBeforeShow||f.show()},_startInTransition:function(){switch(f.transition.in()){case"scrollTop":case"scrollRight":case"scrollBottom":case"scrollLeft":case"scrollHorizontal":case"scrollVertical":f.transition.scroll(f.objects.case,"in",f.settings.speedIn),f.transition.fade(f.objects.contentInner,"in",f.settings.speedIn);break;case"elastic":f.objects.case.css("opacity")<1&&(f.transition.zoom(f.objects.case,"in",f.settings.speedIn),f.transition.fade(f.objects.contentInner,"in",f.settings.speedIn));case"fade":case"fadeInline":f.transition.fade(f.objects.case,"in",f.settings.speedIn),f.transition.fade(f.objects.contentInner,"in",f.settings.speedIn);break;default:f.transition.fade(f.objects.case,"in",0)}f._loading("end"),f.isBusy=!1,f.cache.firstOpened||(f.cache.firstOpened=f.objectData.this),f.objects.info.hide(),setTimeout(function(){f.transition.fade(f.objects.info,"in",f.settings.speedIn)},f.settings.speedIn),f._callHooks(f.settings.onFinish)},_processContent:function(){switch(f.isBusy=!0,f.transition.fade(f.objects.info,"out",0),f.settings.transitionOut){case"scrollTop":case"scrollRight":case"scrollBottom":case"scrollLeft":case"scrollVertical":case"scrollHorizontal":f.objects.case.is(":hidden")?(f.transition.fade(f.objects.contentInner,"out",0),f.transition.fade(f.objects.case,"out",0,0,function(){f._loadContent()})):f.transition.scroll(f.objects.case,"out",f.settings.speedOut,function(){f._loadContent()});break;case"fade":f.objects.case.is(":hidden")?f.transition.fade(f.objects.case,"out",0,0,function(){f._loadContent()}):f.transition.fade(f.objects.case,"out",f.settings.speedOut,0,function(){f._loadContent()});break;case"fadeInline":case"elastic":f.objects.case.is(":hidden")?f.transition.fade(f.objects.case,"out",0,0,function(){f._loadContent()}):f.transition.fade(f.objects.contentInner,"out",f.settings.speedOut,0,function(){f._loadContent()});break;default:f.transition.fade(f.objects.case,"out",0,0,function(){f._loadContent()})}},_handleEvents:function(){f._unbindEvents(),f.objects.nav.children().not(f.objects.close).hide(),f.isSlideshowEnabled()&&(!0!==f.settings.slideshowAutoStart&&!f.isSlideshowStarted||f.objects.nav.hasClass(f.settings.classPrefix+"paused")?f._stopTimeout():f._startTimeout()),f.settings.liveResize&&f._watchResizeInteraction(),f.objects.close.click(function(t){t.preventDefault(),f.close()}),!0===f.settings.closeOnOverlayClick&&f.objects.overlay.css("cursor","pointer").click(function(t){t.preventDefault(),f.close()}),!0===f.settings.useKeys&&f._addKeyEvents(),f.objectData.isPartOfSequence&&(f.objects.nav.attr(f._prefixAttributeName("ispartofsequence"),!0),f.objects.nav.data("items",f._setNavigation()),f.objects.prev.click(function(t){t.preventDefault(),!0!==f.settings.navigateEndless&&f.item.isFirst()||(f.objects.prev.unbind("click"),f.cache.action="prev",f.objects.nav.data("items").prev.click(),f.isSlideshowEnabled()&&f._stopTimeout())}),f.objects.next.click(function(t){t.preventDefault(),!0!==f.settings.navigateEndless&&f.item.isLast()||(f.objects.next.unbind("click"),f.cache.action="next",f.objects.nav.data("items").next.click(),f.isSlideshowEnabled()&&f._stopTimeout())}),f.isSlideshowEnabled()&&(f.objects.play.click(function(t){t.preventDefault(),f._startTimeout()}),f.objects.pause.click(function(t){t.preventDefault(),f._stopTimeout()})),!0===f.settings.swipe&&(r.isPlainObject(r.event.special.swipeleft)&&f.objects.case.on("swipeleft",function(t){t.preventDefault(),f.objects.next.click(),f.isSlideshowEnabled()&&f._stopTimeout()}),r.isPlainObject(r.event.special.swiperight)&&f.objects.case.on("swiperight",function(t){t.preventDefault(),f.objects.prev.click(),f.isSlideshowEnabled()&&f._stopTimeout()})))},_addKeyEvents:function(){r(document).bind("keyup.lightcase",function(t){if(!f.isBusy)switch(t.keyCode){case 27:f.objects.close.click();break;case 37:f.objectData.isPartOfSequence&&f.objects.prev.click();break;case 39:f.objectData.isPartOfSequence&&f.objects.next.click()}})},_startTimeout:function(){f.isSlideshowStarted=!0,f.objects.play.hide(),f.objects.pause.show(),f.cache.action="next",f.objects.nav.removeClass(f.settings.classPrefix+"paused"),f.timeout=setTimeout(function(){f.objects.nav.data("items").next.click()},f.settings.timeout)},_stopTimeout:function(){f.objects.play.show(),f.objects.pause.hide(),f.objects.nav.addClass(f.settings.classPrefix+"paused"),clearTimeout(f.timeout)},_setNavigation:function(){var t=r(f.cache.selector||f.settings.attr),e=f.objectData.sequenceLength-1,i={prev:t.eq(f.objectData.prevIndex),next:t.eq(f.objectData.nextIndex)};return 0<f.objectData.currentIndex?f.objects.prev.show():i.prevItem=t.eq(e),f.objectData.nextIndex<=e?f.objects.next.show():i.next=t.eq(0),!0===f.settings.navigateEndless&&(f.objects.prev.show(),f.objects.next.show()),i},item:{isFirst:function(){return 0===f.objectData.currentIndex},isFirstOpened:function(){return f.objectData.this.is(f.cache.firstOpened)},isLast:function(){return f.objectData.currentIndex===f.objectData.sequenceLength-1}},_cloneObject:function(t){var e=t.clone(),i=t.attr("id");return t.is(":hidden")?(f._cacheObjectData(t),t.attr("id",f.settings.idPrefix+"temp-"+i).empty()):e.removeAttr("id"),e.show()},isMobileDevice:function(){return!!navigator.userAgent.toLowerCase().match(f.settings.mobileMatchExpression)},isTransitionSupported:function(){var t=f.objects.body.get(0),e=!1,i={transition:"",WebkitTransition:"-webkit-",MozTransition:"-moz-",OTransition:"-o-",MsTransition:"-ms-"};for(var s in i)i.hasOwnProperty(s)&&s in t.style&&(f.support.transition=i[s],e=!0);return e},transition:{in:function(){return f.settings.transitionOpen&&!f.cache.firstOpened?f.settings.transitionOpen:f.settings.transitionIn},fade:function(t,e,i,s,n){var a="in"===e,o={},c=t.css("opacity"),r={},l=s||(a?1:0);!f.isOpen&&a||(o.opacity=c,r.opacity=l,t.css(f.support.transition+"transition","none"),t.css(o).show(),f.support.transitions?(r[f.support.transition+"transition"]=i+"ms ease",setTimeout(function(){t.css(r),setTimeout(function(){t.css(f.support.transition+"transition",""),!n||!f.isOpen&&a||n()},i)},15)):(t.stop(),t.animate(r,i,n)))},scroll:function(t,e,i,s){var n="in"===e,a=n?f.settings.transitionIn:f.settings.transitionOut,o="left",c={},r=n?0:1,l=n?"-50%":"50%",d={},u=n?1:0,h=n?"50%":"-50%";if(f.isOpen||!n){switch(a){case"scrollTop":o="top";break;case"scrollRight":l=n?"150%":"50%",h=n?"50%":"150%";break;case"scrollBottom":o="top",l=n?"150%":"50%",h=n?"50%":"150%";break;case"scrollHorizontal":l=n?"150%":"50%",h=n?"50%":"-50%";break;case"scrollVertical":o="top",l=n?"-50%":"50%",h=n?"50%":"150%"}if("prev"===f.cache.action)switch(a){case"scrollHorizontal":l=n?"-50%":"50%",h=n?"50%":"150%";break;case"scrollVertical":l=n?"150%":"50%",h=n?"50%":"-50%"}c.opacity=r,c[o]=l,d.opacity=u,d[o]=h,t.css(f.support.transition+"transition","none"),t.css(c).show(),f.support.transitions?(d[f.support.transition+"transition"]=i+"ms ease",setTimeout(function(){t.css(d),setTimeout(function(){t.css(f.support.transition+"transition",""),!s||!f.isOpen&&n||s()},i)},15)):(t.stop(),t.animate(d,i,s))}},zoom:function(t,e,i,s){var n="in"===e,a={},o=t.css("opacity"),c=n?"scale(0.75)":"scale(1)",r={},l=n?1:0,d=n?"scale(1)":"scale(0.75)";!f.isOpen&&n||(a.opacity=o,a[f.support.transition+"transform"]=c,r.opacity=l,t.css(f.support.transition+"transition","none"),t.css(a).show(),f.support.transitions?(r[f.support.transition+"transform"]=d,r[f.support.transition+"transition"]=i+"ms ease",setTimeout(function(){t.css(r),setTimeout(function(){t.css(f.support.transition+"transform",""),t.css(f.support.transition+"transition",""),!s||!f.isOpen&&n||s()},i)},15)):(t.stop(),t.animate(r,i,s)))}},_callHooks:function(t){"object"==typeof t&&r.each(t,function(t,e){"function"==typeof e&&e.call(f.origin)})},_cacheObjectData:function(t){r.data(t,"cache",{id:t.attr("id"),content:t.html()}),f.cache.originalObject=t},_restoreObject:function(){var t=r('[id^="'+f.settings.idPrefix+'temp-"]');t.attr("id",r.data(f.cache.originalObject,"cache").id),t.html(r.data(f.cache.originalObject,"cache").content)},resize:function(t,e){f.isOpen&&(f.isSlideshowEnabled()&&f._stopTimeout(),"object"==typeof e&&null!==e&&(e.width&&f.cache.object.attr(f._prefixAttributeName("width"),e.width),e.maxWidth&&f.cache.object.attr(f._prefixAttributeName("max-width"),e.maxWidth),e.height&&f.cache.object.attr(f._prefixAttributeName("height"),e.height),e.maxHeight&&f.cache.object.attr(f._prefixAttributeName("max-height"),e.maxHeight)),f.dimensions=f.getViewportDimensions(),f._calculateDimensions(f.cache.object),f._callHooks(f.settings.onResize))},_watchResizeInteraction:function(){r(window).resize(f.resize)},_unwatchResizeInteraction:function(){r(window).off("resize",f.resize)},_switchToFullScreenMode:function(){f.settings.shrinkFactor=1,f.settings.overlayOpacity=1,r("html").addClass(f.settings.classPrefix+"fullScreenMode")},_open:function(){switch(f.isOpen=!0,f.support.transitions=!!f.settings.cssTransitions&&f.isTransitionSupported(),f.support.mobileDevice=f.isMobileDevice(),f.support.mobileDevice&&(r("html").addClass(f.settings.classPrefix+"isMobileDevice"),f.settings.fullScreenModeForMobile&&f._switchToFullScreenMode()),f.settings.transitionIn||(f.settings.transitionIn=f.settings.transition),f.settings.transitionOut||(f.settings.transitionOut=f.settings.transition),f.transition.in()){case"fade":case"fadeInline":case"elastic":case"scrollTop":case"scrollRight":case"scrollBottom":case"scrollLeft":case"scrollVertical":case"scrollHorizontal":f.objects.case.is(":hidden")&&(f.objects.close.css("opacity",0),f.objects.overlay.css("opacity",0),f.objects.case.css("opacity",0),f.objects.contentInner.css("opacity",0)),f.transition.fade(f.objects.overlay,"in",f.settings.speedIn,f.settings.overlayOpacity,function(){f.transition.fade(f.objects.close,"in",f.settings.speedIn),f._handleEvents(),f._processContent()});break;default:f.transition.fade(f.objects.overlay,"in",0,f.settings.overlayOpacity,function(){f.transition.fade(f.objects.close,"in",0),f._handleEvents(),f._processContent()})}f.objects.document.addClass(f.settings.classPrefix+"open"),f.objects.case.attr("aria-hidden","false")},show:function(){f._callHooks(f.settings.onBeforeCalculateDimensions),f._calculateDimensions(f.cache.object),f._callHooks(f.settings.onAfterCalculateDimensions),f._startInTransition()},close:function(){switch(f.isOpen=!1,f.isSlideshowEnabled()&&(f._stopTimeout(),f.isSlideshowStarted=!1,f.objects.nav.removeClass(f.settings.classPrefix+"paused")),f.objects.loading.hide(),f._unbindEvents(),f._unwatchResizeInteraction(),r("html").removeClass(f.settings.classPrefix+"open"),f.objects.case.attr("aria-hidden","true"),f.objects.nav.children().hide(),f.objects.close.hide(),f._callHooks(f.settings.onClose),f.transition.fade(f.objects.info,"out",0),f.settings.transitionClose||f.settings.transitionOut){case"fade":case"fadeInline":case"scrollTop":case"scrollRight":case"scrollBottom":case"scrollLeft":case"scrollHorizontal":case"scrollVertical":f.transition.fade(f.objects.case,"out",f.settings.speedOut,0,function(){f.transition.fade(f.objects.overlay,"out",f.settings.speedOut,0,function(){f.cleanup()})});break;case"elastic":f.transition.zoom(f.objects.case,"out",f.settings.speedOut,function(){f.transition.fade(f.objects.overlay,"out",f.settings.speedOut,0,function(){f.cleanup()})});break;default:f.cleanup()}},_unbindEvents:function(){f.objects.overlay.unbind("click"),r(document).unbind("keyup.lightcase"),f.objects.case.unbind("swipeleft").unbind("swiperight"),f.objects.prev.unbind("click"),f.objects.next.unbind("click"),f.objects.play.unbind("click"),f.objects.pause.unbind("click"),f.objects.close.unbind("click")},_cleanupDimensions:function(){var t=f.objects.contentInner.css("opacity");f.objects.case.css({width:"",height:"",top:"",left:"","margin-top":"","margin-left":""}),f.objects.contentInner.removeAttr("style").css("opacity",t),f.objects.contentInner.children().removeAttr("style")},cleanup:function(){f._cleanupDimensions(),f.objects.loading.hide(),f.objects.overlay.hide(),f.objects.case.hide(),f.objects.prev.hide(),f.objects.next.hide(),f.objects.play.hide(),f.objects.pause.hide(),f.objects.document.removeAttr(f._prefixAttributeName("type")),f.objects.nav.removeAttr(f._prefixAttributeName("ispartofsequence")),f.objects.contentInner.empty().hide(),f.objects.info.children().empty(),f.cache.originalObject&&f._restoreObject(),f._callHooks(f.settings.onCleanup),f.cache={}},_matchMedia:function(){return window.matchMedia||window.msMatchMedia},_devicePixelRatio:function(){return window.devicePixelRatio||1},_isPublicMethod:function(t){return"function"==typeof f[t]&&"_"!==t.charAt(0)},_export:function(){window.lightcase={},r.each(f,function(t){f._isPublicMethod(t)&&(lightcase[t]=f[t])})}};f._export(),r.fn.lightcase=function(t){return f._isPublicMethod(t)?f[t].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof t&&t?void r.error("Method "+t+" does not exist on jQuery.lightcase"):f.init.apply(this,arguments)}}(jQuery);