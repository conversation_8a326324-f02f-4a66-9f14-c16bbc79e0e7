/* reset css start */
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600&family=Maven+Pro:wght@400;500;600;700&display=swap");
html {
    scroll-behavior: smooth;
}
body {
    font-family: "Open Sans", sans-serif;
    color: #464646;
    font-size: 1rem;
    padding: 0;
    margin: 0;
    font-weight: 400;
    position: relative;
    line-height: 1.7;
    background-color: #f9fbfc;
    -webkit-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;
    overflow-x: hidden;
}
body.page-trns-active {
    position: relative;
}
body.page-trns-active::after {
    position: absolute;
    content: "\f110";
    font-family: 'Line Awesome Free';
    font-weight: 900;
    color: #fff;
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    font-size: 4.5rem;
    z-index: 999;
    animation: spinRoll 1s infinite linear;
}
body.page-trns-active::before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #002046;
    z-index: 99;
    opacity: 0.65;
}
@-webkit-keyframes spinRoll {
    from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-moz-keyframes spinRoll {
    from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-ms-keyframes spinRoll {
    from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes spinRoll {
    from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
section {
    background-color: #fff;
}
img {
    max-width: 100%;
    height: auto;
    user-select: none;
}
select {
    cursor: pointer;
}
ul, ol {
    padding: 0;
    margin: 0;
    list-style: none;
}
button {
    cursor: pointer;
}
*:focus {
    outline: none;
}
button {
    border: none;
}
button:focus {
    outline: none;
}
span {
    display: inline-block;
}
a:hover {
    color: #4582ff;
}
/* reset css end */
/* global css strat */
.text--primary {
    color: #7367f0 !important;
}
.text--secondary {
    color: #868e96 !important;
}
.text--success {
    color: #28c76f !important;
}
.text--danger {
    color: #ea5455 !important;
}
.text--warning {
    color: #ff9f43 !important;
}
.text--info {
    color: #1e9ff2 !important;
}
.text--dark {
    color: #10163A !important;
}
.text--muted {
    color: #cccccc !important;
}
.text--base {
    color: #4582ff !important;
}
.text--dark {
    color: #373e4a !important;
}
/* background color css start */
.bg--primary {
    background-color: #7367f0 !important;
}
.bg--secondary {
    background-color: #868e96 !important;
}
.bg--success {
    background-color: #28c76f !important;
}
.bg--danger {
    background-color: #ea5455 !important;
}
.bg--warning {
    background-color: #ff9f43 !important;
}
.bg--info {
    background-color: #1e9ff2 !important;
}
.bg--dark {
    background-color: #10163A !important;
}
.bg--light {
    background-color: #eef4ff !important;
}
.bg--base {
    background-color: #4582ff !important;
}
/* background color css end */
.mb-30 {
    margin-bottom: 30px;
}
.mb-none-30 {
    margin-bottom: -30px;
}
.pt-50 {
    padding-top: 50px;
}
.pb-50 {
    padding-bottom: 50px;
}
.pt-100 {
    padding-top: 100px;
}
.pb-100 {
    padding-bottom: 100px;
}
.bg_img {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}
.section--bg {
    background-color: #F5F7FA;
}
.bg--one {
    background-color: #071e3e;
}
.slick-arrow {
    cursor: pointer;
}
.main-wrapper {
    position: relative;
   
}
.section-header {
    margin-bottom: 4.0625rem;
}
.section-title {
    font-size: 3.25rem;
}
@media (max-width: 1199px) {
    .section-title {
        font-size: 3rem;
    }
}
@media (max-width: 991px) {
    .section-title {
        font-size: 2.25rem;
    }
}
.section-subtitle {
    font-family: "Maven Pro", sans-serif;
    font-size: 1rem;
    font-weight: 500;
}
.section-subtitle.border-left {
    padding-left: 2.1875rem;
    position: relative;
    z-index: 1;
}
.section-subtitle.border-left::before {
    position: absolute;
    content: '';
    top: 50%;
    left: 0;
    width: 25px;
    height: 2px;
    background-color: #4582ff;
    margin-top: -1px;
}
a.text-white:hover {
    color: #4582ff !important;
}
.text--link {
    text-decoration: underline;
}
.text--link:hover {
    text-decoration: underline;
}
.has--link {
    position: relative;
}
.has--link .item--link {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}
.custom--dropdown .dropdown-toggle.no-arrow::after {
    display: none;
}
.custom--dropdown .dropdown-toggle::after {
    content: "\f107";
    border: none;
    font-family: 'Line Awesome Free';
    font-weight: 900;
    -webkit-transform: translateY(3px);
    -ms-transform: translateY(3px);
    transform: translateY(3px);
}
.custom--dropdown .dropdown-menu {
    border-color: #e5e5e5;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
}
.custom--dropdown .dropdown-menu li {
    border-bottom: 1px dashed #e5e5e5;
}
.custom--dropdown .dropdown-menu li:last-child {
    border-bottom: none;
}
.custom--dropdown .dropdown-menu li .dropdown-item {
    color: #464646;
    font-size: 0.875rem;
}
.custom--dropdown .dropdown-menu li .dropdown-item:hover {
    color: #4582ff;
    background-color: rgba(69, 130, 255, 0.05);
}
.custom--accordion .accordion-item + .accordion-item {
    margin-top: 1.25rem;
}
.custom--accordion .accordion-item {
    border: 1px solid rgba(69, 130, 255, 0.5);
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.custom--accordion .accordion-item:first-child .accordion-button {
    border-top: none;
}
.custom--accordion .accordion-item:last-child .accordion-button {
    border-bottom: none;
}
.custom--accordion .accordion-button {
    padding: 1.25rem 1.5625rem;
    background-color: rgba(69, 130, 255, 0.05);
    font-size: 1.125rem;
    position: relative;
    text-align: left;
}
.custom--accordion .accordion-button::after {
    position: absolute;
    top: 1.25rem;
    right: 0.8125rem;
    font-size: 1.0625rem;
    content: '\f107';
    font-family: 'Line Awesome Free';
    font-weight: 900;
    background-image: none;
    color: #000;
}
.custom--accordion .accordion-button:not(.collapsed) {
    background-color: #4582ff;
    color: #fff;
}
.custom--accordion .accordion-button:not(.collapsed)::after {
    color: #fff;
}
.custom--accordion .accordion-button:focus {
    box-shadow: none;
    outline: none;
    border-color: transparent;
}
.custom--accordion .accordion-body {
    padding: 1.25rem 1.5625rem;
}
.page-breadcrumb {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: 0.9375rem;
}
.page-breadcrumb li {
    color: rgba(255, 255, 255, 0.8);
    text-transform: capitalize;
}
.page-breadcrumb li::after {
    content: '-';
    color: #ffffff;
    margin: 0 0.3125rem;
}
.page-breadcrumb li:first-child::before {
    content: "\f015";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: #4582ff;
    margin-right: 0.375rem;
}
.page-breadcrumb li:last-child::after {
    display: none;
}
.page-breadcrumb li a {
    color: #ffffff;
    text-transform: capitalize;
}
.page-breadcrumb li a:hover {
    color: #4582ff;
}
.cmn-list li + li {
    margin-top: 0.9375rem;
}
.cmn-list li {
    position: relative;
    padding-left: 2.1875rem;
}
.cmn-list li::before {
    position: absolute;
    top: 0;
    left: 0;
    font-family: 'Line Awesome Free';
    font-weight: 900;
    content: "\f058";
    font-size: 1.75rem;
    color: #4582ff;
    margin-right: 0.5rem;
    line-height: 1;
}
.cmn-list-two li {
    padding: 0.375rem 0.9375rem;
    font-size: 0.875rem;
}
.cmn-list-two li:nth-child(even) {
    background-color: #EBF5F5;
}
.number-list {
    list-style: decimal;
    padding-left: 1.125rem;
}
.number-list li + li {
    margin-top: 0.625rem;
}
.disc-list li + li {
    margin-top: 0.625rem;
}
.disc-list li {
    position: relative;
    padding-left: 0.9375rem;
}
.disc-list li::before {
    position: absolute;
    content: '';
    top: 50%;
    left: 0;
    width: 0.375rem;
    height: 0.375rem;
    margin-top: -0.1875rem;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    background-color: #bdbdbd;
}
.caption-list li {
    display: flex;
    flex-wrap: wrap;
    padding: 0.625rem 0;
    font-size: 0.9375rem;
    border-bottom: 1px dashed #cacaca;
}
.caption-list li:first-child {
    padding-top: 0;
}
.caption-list li:last-child {
    padding-bottom: 0;
    border-bottom: none;
}
.caption-list li .caption {
    width: 30%;
    font-family: "Maven Pro", sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    position: relative;
}
@media (max-width: 480px) {
    .caption-list li .caption {
        width: 35%;
    }
}
.caption-list li .caption::after {
    position: absolute;
    content: ':';
    top: 0;
    right: 0;
}
.caption-list li .value {
    width: 70%;
    padding-left: 0.9375rem;
}
@media (max-width: 480px) {
    .caption-list li .value {
        width: 65%;
    }
}
.caption-list-two {
    padding: 0.625rem 0.9375rem;
    background-color: rgba(69, 130, 255, 0.1);
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.caption-list-two li {
    font-family: "Maven Pro", sans-serif;
    font-weight: 500;
    color: #373e4a;
    font-size: 0.875rem;
    padding: 0.5rem 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    border-bottom: 1px dashed #b1b1b1;
}
.caption-list-two li:first-child {
    padding-top: 0;
}
.caption-list-two li:last-child {
    padding-bottom: 0;
    border-bottom: none;
}
.caption-list-two li .caption {
    width: 20%;
    position: relative;
}
.caption-list-two li .caption::after {
    position: absolute;
    content: ':';
    top: 0;
    right: 0;
}
.caption-list-two li .value {
    width: 80%;
    text-align: right;
}
body, .btn--base.btn--custom, .custom--form-group .form--control ~ label, .header__bottom, .header .main-menu li.menu_has_children > a::before, .header .main-menu li .sub-menu, .header .main-menu li .sub-menu li a, .header .main-menu li .sub-menu li a::before, .header-user-menu li a, .header-login-btn, .header-login-btn i, .sidebar-menu__link, .d-widget, .quick-link-card .icon, .quick-link-card .icon::before, .quick-link-card .caption, .bank-card.add-bank, .agent-dashboard .d-sidebar {
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.object-fit--cover {
    object-fit: cover;
    -o-object-fit: cover;
    object-position: center;
    -o-object-position: center;
}
.pagination {
    margin: -0.3125rem -0.4375rem;
    flex-wrap: wrap;
    justify-content: end;
    
}

.pagination .page-item {
    margin: 0.3125rem 0.4375rem;
}

.pagination .page-item.active .page-link {
    background-color: #4582ff;
    color: #fff;
}

.pagination .page-item .page-link {
    background-color: #e4e4e4;
    width: 2.8125rem;
    height: 2.8125rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #777;
}

.pagination .page-item .page-link:hover {
    background-color: #4582ff;
    border-color: #4582ff;
    color: #fff;
}

.pagination-md .page-item .page-link {
    width: 2.5rem;
    height: 2.5rem;
}

.pagination-sm .page-item .page-link {
    width: 2.1875rem;
    height: 2.1875rem;
    font-size: 0.875rem;
}
.video--btn {
    width: 7.5rem;
    height: 5.625rem;
    background-color: #4582ff;
    color: #fff;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    bottom: 0;
    right: 0;
    font-size: 2.625rem;
    border: 4px solid #fff;
}
.video--btn:hover {
    color: #fff;
}
.shake {
    animation: shake 0.5s 1 linear;
}
@-webkit-keyframes shake {
    0%, 100% {
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translateX(-10px);
        -ms-transform: translateX(-10px);
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        -webkit-transform: translateX(10px);
        -ms-transform: translateX(10px);
        transform: translateX(10px);
    }
}
@-moz-keyframes shake {
    0%, 100% {
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translateX(-10px);
        -ms-transform: translateX(-10px);
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        -webkit-transform: translateX(10px);
        -ms-transform: translateX(10px);
        transform: translateX(10px);
    }
}
@-ms-keyframes shake {
    0%, 100% {
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translateX(-10px);
        -ms-transform: translateX(-10px);
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        -webkit-transform: translateX(10px);
        -ms-transform: translateX(10px);
        transform: translateX(10px);
    }
}
@keyframes shake {
    0%, 100% {
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translateX(-10px);
        -ms-transform: translateX(-10px);
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        -webkit-transform: translateX(10px);
        -ms-transform: translateX(10px);
        transform: translateX(10px);
    }
}
.fadeInUp {
    -webkit-animation-name: fadeInUp;
    animation-name: fadeInUp;
}
@-webkit-keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
        -ms-transform: translateY(20px);
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}
@-moz-keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
        -ms-transform: translateY(20px);
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}
@-ms-keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
        -ms-transform: translateY(20px);
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}
@keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
        -ms-transform: translateY(20px);
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}
.fadeInLeft {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft;
}
@-webkit-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
        -ms-transform: translateX(-20px);
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@-moz-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
        -ms-transform: translateX(-20px);
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@-ms-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
        -ms-transform: translateX(-20px);
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
        -ms-transform: translateX(-20px);
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
.fadeInRight {
    -webkit-animation-name: fadeInRight;
    animation-name: fadeInRight;
}
@-webkit-keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
        -ms-transform: translateX(20px);
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@-moz-keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
        -ms-transform: translateX(20px);
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@-ms-keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
        -ms-transform: translateX(20px);
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
        -ms-transform: translateX(20px);
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
.slideInLeft {
    -webkit-animation-name: slideInLeft;
    animation-name: slideInLeft;
}
@-webkit-keyframes slideInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-2000px);
        transform: translateX(-2000px);
    }
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}
@-moz-keyframes slideInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-2000px);
        transform: translateX(-2000px);
    }
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}
@-ms-keyframes slideInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-2000px);
        transform: translateX(-2000px);
    }
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}
@keyframes slideInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-2000px);
        transform: translateX(-2000px);
    }
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}
.slideInRight {
    -webkit-animation-name: slideInRight;
    animation-name: slideInRight;
}
@-webkit-keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
@-moz-keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
@-ms-keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
@keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
/* global css end */
/* preloader css start */
@-webkit-keyframes preloader-inside-white {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@-moz-keyframes preloader-inside-white {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@-ms-keyframes preloader-inside-white {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@keyframes preloader-inside-white {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@-webkit-keyframes preloader-inside-red {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    30% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@-moz-keyframes preloader-inside-red {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    30% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@-ms-keyframes preloader-inside-red {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    30% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@keyframes preloader-inside-red {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    30% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

.preloader {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999999999;
    background: white;
    text-align: center;
}

.preloader .preloader-container {
    display: inline-block;
    width: 100px;
    height: 100px;
    position: relative;
}

.preloader .animated-preloader {
    display: inline-block;
    width: 100px;
    height: 100px;
    position: absolute;
    top: 0;
    left: 0;
    background: #0087FF;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.preloader .animated-preloader::after {
    content: '';
    display: inline-block;
    width: 100px;
    height: 100px;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    background: white;
    -webkit-animation: preloader-inside-white 1s ease-in-out infinite;
    -ms-animation: preloader-inside-white 1s ease-in-out infinite;
    animation: preloader-inside-white 1s ease-in-out infinite;
}

.preloader .animated-preloader::before {
    content: '';
    display: inline-block;
    width: 100px;
    height: 100px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    background: #0087FF;
    -webkit-animation: preloader-inside-red 1s ease-in-out infinite;
    -ms-animation: preloader-inside-red 1s ease-in-out infinite;
    animation: preloader-inside-red 1s ease-in-out infinite;
}
/* preloader css end */
h1 {
    font-size: 3.875rem;
}
h2 {
    font-size: 2rem;
}
@media (max-width: 991px) {
    h2 {
        font-size: 1.875rem;
    }
}
@media (max-width: 575px) {
    h2 {
        font-size: 1.75rem;
    }
}
h3 {
    font-size: 1.5rem;
}
h4 {
    font-size: 1.375rem;
}
@media (max-width: 767px) {
    h4 {
        font-size: 1.25rem;
    }
}
h5 {
    font-size: 1.25rem;
}
@media (max-width: 767px) {
    h5 {
        font-size: 1.125rem;
    }
}
h6 {
    font-size: 1.125rem;
}
h1, h2, h3, h4, h5, h6 {
    font-family: "Maven Pro", sans-serif;
    color: #373e4a;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
    word-break: break-word;
}
h1 > a, h2 > a, h3 > a, h4 > a, h5 > a, h6 > a {
    font-family: "Maven Pro", sans-serif;
    color: #373e4a;
    font-weight: 700;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    line-height: 1.2;
    word-break: break-word;
}
p, li, span {
    margin: 0;
}
a {
    text-decoration: none;
    display: inline-block;
    font-family: "Open Sans", sans-serif;
    font-weight: 400;
}
a:hover {
    text-decoration: none;
}
.font-size--18px {
    font-size: 1.125rem !important;
}
.font-size--16px {
    font-size: 1rem !important;
}
.font-size--14px {
    font-size: 0.875rem !important;
}
.font-size--12px {
    font-size: 0.75rem !important;
}
/* button css start */
button:focus {
    outline: none;
}
[class*="btn--"]:not(.btn--link):not(.btn--light) {
    color: #fff;
}
.btn {
    padding: 0.875rem 1.875rem;
}
.btn--primary {
    background-color: #7367f0 !important;
}
.btn--primary:hover {
    background-color: #5e50ee !important;
}
.btn--secondary {
    background-color: #868e96 !important;
}
.btn--secondary:hover {
    background-color: #78818a !important;
}
.btn--success {
    background-color: #28c76f !important;
}
.btn--success:hover {
    background-color: #24b263 !important;
}
.btn--danger {
    background-color: #ea5455 !important;
}
.btn--danger:hover {
    background-color: #e73d3e !important;
}
.btn--warning {
    background-color: #ff9f43 !important;
}
.btn--warning:hover {
    background-color: #ff922a !important;
}
.btn--info {
    background-color: #1e9ff2 !important;
}
.btn--info:hover {
    background-color: #0d93e9 !important;
}
.btn--light {
    background-color: #eef4ff !important;
}
.btn--light:hover {
    background-color: #d5e4ff !important;
}
.btn--dark {
    background-color: #10163A !important;
    color: #fff;
}
.btn--dark:hover {
    background-color: #0a0e26 !important;
    color: #fff;
}
.btn--link {
    color: #7367f0;
}
.btn--base {
    background-color: #4582ff;
    color: #fff;
}
.btn--base:hover {
    background-color: #2c71ff;
    color: #fff;
}
.btn--base.btn--custom {
    outline: 2px solid rgba(255, 255, 255, 0.7);
    outline-offset: -6px;
}
.btn--base.btn--custom:hover {
    outline-offset: 0;
    outline: 2px solid rgba(255, 255, 255, 0);
}
.text-btn {
    padding: 0;
    color: #464646;
    background-color: transparent;
}
.arrow-btn {
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    color: #464646;
    display: flex;
    flex-wrap: wrap;
    min-height: 4.375rem;
}
.arrow-btn i {
    width: 95px;
    font-size: calc((5.9375rem / 2) - 0.75rem);
    -webkit-clip-path: polygon(75% 0%, 100% 50%, 75% 100%, 0% 100%, 20% 50%, 0% 0%);
    clip-path: polygon(75% 0%, 100% 50%, 75% 100%, 0% 100%, 20% 50%, 0% 0%);
    background-color: #4582ff;
    color: #fff;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
}
.arrow-btn span {
    width: calc(100% - 95px);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-family: "Maven Pro", sans-serif;
    background-color: #fff;
    z-index: 1;
    position: relative;
    padding: 0.625rem;
}
.arrow-btn span::before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: calc(100% + 50px);
    height: 100%;
    background-color: #fff;
    box-shadow: 0 0 3px #2f2f2f1f;
    border: 1px solid #d6d6d6;
    left: -50px;
    z-index: -1;
    border-radius: 0 5px 5px 0;
}
.btn-outline--primary {
    color: #7367f0;
    border-color: #7367f0;
}
.btn-outline--primary:hover {
    background-color: #7367f0;
    color: #ffffff;
}
.btn-outline--secondary {
    color: #868e96;
    border-color: #868e96;
}
.btn-outline--secondary:hover {
    background-color: #868e96;
    color: #ffffff;
}
.btn-outline--success {
    color: #28c76f;
    border-color: #28c76f;
}
.btn-outline--success:hover {
    background-color: #28c76f;
    color: #ffffff;
}
.btn-outline--danger {
    color: #ea5455;
    border-color: #ea5455;
}
.btn-outline--danger:hover {
    background-color: #ea5455;
    color: #ffffff;
}
.btn-outline--warning {
    color: #ff9f43;
    border-color: #ff9f43;
}
.btn-outline--warning:hover {
    background-color: #ff9f43;
    color: #ffffff;
}
.btn-outline--info {
    color: #1e9ff2;
    border-color: #1e9ff2;
}
.btn-outline--info:hover {
    background-color: #1e9ff2;
    color: #ffffff;
}
.btn-outline--light {
    color: #eef4ff;
    border-color: #eef4ff;
}
.btn-outline--light:hover {
    background-color: #eef4ff;
    color: #ffffff;
}
.btn-outline--dark {
    color: #10163A;
    border-color: #10163A;
}
.btn-outline--dark:hover {
    background-color: #10163A;
    color: #ffffff;
}
.btn-outline--base {
    color: #4582ff;
    border: 1px solid #4582ff;
}
.btn-outline--base:hover {
    background-color: #4582ff;
    color: #fff;
}
.btn-shadow--primary {
    box-shadow: 0 0 6px 1px rgba(115, 103, 240, 0.35);
}
.btn-shadow--secondary {
    box-shadow: 0 0 6px 1px rgba(134, 142, 150, 0.35);
}
.btn-shadow--success {
    box-shadow: 0 0 6px 1px rgba(40, 199, 111, 0.35);
}
.btn-shadow--danger {
    box-shadow: 0 0 6px 1px rgba(234, 84, 85, 0.35);
}
.btn-shadow--warning {
    box-shadow: 0 0 6px 1px rgba(255, 159, 67, 0.35);
}
.btn-shadow--info {
    box-shadow: 0 0 6px 1px rgba(30, 159, 242, 0.35);
}
.btn-shadow--light {
    box-shadow: 0 0 6px 1px rgba(238, 244, 255, 0.35);
}
.btn-shadow--dark {
    box-shadow: 0 0 6px 1px rgba(16, 22, 58, 0.35);
}
.btn-shadow--base {
    box-shadow: 0 0 6px 1px rgba(69, 130, 255, 0.35);
}
.btn--capsule {
    border-radius: 999px;
    -webkit-border-radius: 999px;
    -moz-border-radius: 999px;
    -ms-border-radius: 999px;
    -o-border-radius: 999px;
}
.icon-btn {
    width: 1.5625rem;
   height: 1.5625rem;
    background-color: #4582ff;
    color: #fff;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}
.icon-btn:hover {
    color: #fff;
}
.video-btn {
    display: inline-flex;
    align-items: center;
}
.video-btn .icon {
    width: 3.75rem;
    height: 3.75rem;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background-color: #4582ff;
    color: #fff;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    font-size: calc(3.75rem / 2);
    position: relative;
    z-index: 1;
}
.video-btn .icon::before, .video-btn .icon::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: 74px;
    background-color: #4582ff;
    opacity: 0.15;
    z-index: -1;
}
.video-btn .icon::after {
    -webkit-animation: outer-ripple 2000ms linear infinite;
    -moz-animation: outer-ripple 2000ms linear infinite;
    animation: outer-ripple 2000ms linear infinite;
}
.video-btn .icon::before {
    -webkit-animation: inner-ripple 2000ms linear infinite;
    -moz-animation: inner-ripple 2000ms linear infinite;
    animation: inner-ripple 2000ms linear infinite;
}
.video-btn span {
    margin-left: 10px;
}
@-webkit-keyframes outer-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        -webkit-filter: alpha(opacity=50);
    }
    80% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
    100% {
        transform: scale(2.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(2.5);
        -moz-transform: scale(2.5);
        -ms-transform: scale(2.5);
        -o-transform: scale(2.5);
    }
}
@-moz-keyframes outer-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        -webkit-filter: alpha(opacity=50);
    }
    80% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
    100% {
        transform: scale(2.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(2.5);
        -moz-transform: scale(2.5);
        -ms-transform: scale(2.5);
        -o-transform: scale(2.5);
    }
}
@-ms-keyframes outer-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        -webkit-filter: alpha(opacity=50);
    }
    80% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
    100% {
        transform: scale(2.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(2.5);
        -moz-transform: scale(2.5);
        -ms-transform: scale(2.5);
        -o-transform: scale(2.5);
    }
}
@keyframes outer-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        -webkit-filter: alpha(opacity=50);
    }
    80% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
    100% {
        transform: scale(2.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(2.5);
        -moz-transform: scale(2.5);
        -ms-transform: scale(2.5);
        -o-transform: scale(2.5);
    }
}
@-webkit-keyframes inner-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    30% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    100% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
}
@-moz-keyframes inner-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    30% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    100% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
}
@-ms-keyframes inner-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    30% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    100% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
}
@keyframes inner-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    30% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    100% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
}
.btn--group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
    margin-left: -0.625rem;
    margin-right: -0.625rem;
}
.btn--group *[class*="btn"] {
    margin: 0.3125rem 0.625rem;
    align-items: center;
}
.btn--group *[class*="btn"].d-flex {
    padding: 0.5rem 2.1875rem;
}
.btn--group.style--two {
    margin-left: -0.3125rem;
    margin-right: -0.3125rem;
}
.btn--group.style--two *[class*="btn"] {
    margin: 0.1875rem 0.3125rem;
}
[class*="btn"].btn-md {
    padding: 0.625rem 1.25rem;
}
[class*="btn"].btn-sm {
    padding: 0.375rem 0.625rem;
}
/* button css end */
.badge {
    border-radius: 40px !important;
    padding: 3px 20px !important;
}
.badge--primary {
    background-color: rgba(115, 103, 240, 0.15);
    border: 1px solid #7367f0;
    color: #7367f0;
}
.badge--secondary {
    background-color: rgba(134, 142, 150, 0.15);
    border: 1px solid #868e96;
    color: #868e96;
}
.badge--success {
    background-color: rgba(40, 199, 111, 0.15);
    border: 1px solid #28c76f;
    color: #28c76f;
}
.badge--danger {
    background-color: rgba(234, 84, 85, 0.15);
    border: 1px solid #ea5455;
    color: #ea5455;
}
.badge--warning {
    background-color: rgba(255, 159, 67, 0.15);
    border: 1px solid #ff9f43;
    color: #ff9f43;
}
.badge--info {
    background-color: rgba(30, 159, 242, 0.15);
    border: 1px solid #1e9ff2;
    color: #1e9ff2;
}
.badge--light {
    background-color: rgba(238, 244, 255, 0.15);
    border: 1px solid #eef4ff;
    color: #eef4ff;
}
.badge--dark {
    background-color: rgba(16, 22, 58, 0.15);
    border: 1px solid #10163A;
    color: #10163A;
}
.badge--base {
    background-color: rgba(69, 130, 255, 0.15);
    border: 1px solid #4582ff;
    color: #4582ff;
}
/* table css start */
.custom--table {
    background-color: #fff;
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
}
.custom--table.white-space-nowrap th {
    white-space: nowrap;
}
.custom--table thead tr {
    box-shadow: 0 5px 10px 2px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
}
.custom--table thead th {
    border-top: none;
    padding: 0.625rem 1.25rem;
    color: #fff;
    background-color: #002046;
    border: none;
    font-size: 0.8125rem;
    text-transform: uppercase;
    font-weight: 700;
}
.custom--table thead th:first-child {
    border-radius: 5px 0 0 5px;
    -webkit-border-radius: 5px 0 0 5px;
    -moz-border-radius: 5px 0 0 5px;
    -ms-border-radius: 5px 0 0 5px;
    -o-border-radius: 5px 0 0 5px;
}
.custom--table thead th:last-child {
    border-radius: 0 5px 5px 0;
    -webkit-border-radius: 0 5px 5px 0;
    -moz-border-radius: 0 5px 5px 0;
    -ms-border-radius: 0 5px 5px 0;
    -o-border-radius: 0 5px 5px 0;
}
.custom--table tbody td {
    border-top: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07);
    padding: 0.9375rem 1.25rem;
    color: #464646;
    vertical-align: middle;
    font-size: 0.875rem;
}
@media screen and (max-width: 1199px) and (min-width: 991px) {
    .custom--table tbody td {
        padding: 0.9375rem 11px;
    }
}
.custom--table tbody tr {
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.custom--table tbody tr:nth-child(even) {
    background-color: #afb1b50d;
}
.custom--table tbody tr:last-child td {
    border-bottom: none;
}
[data-label] {
    position: relative;
}
[data-label]::before {
    position: absolute;
    content: attr(data-label);
    font-weight: 700;
    color: #000000;
    top: 0;
    left: 0;
    padding: 0.8125rem 0.9375rem;
    display: none;
    font-size: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    padding-left: 15px;
}
@media (max-width: 991px) {
    .table-responsive--md thead {
        display: none;
    }
    .table-responsive--md tbody tr:nth-child(odd) {
        background-color: aliceblue;
    }
    .table-responsive--md tr th, .table-responsive--md tr td {
        display: block;
        padding-left: 45% !important;
        text-align: right !important;
        border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
    }
    .table-responsive--md tr th:first-child, .table-responsive--md tr td:first-child {
        border-top: none !important;
    }
    .table-responsive--md [data-label]::before {
        display: block;
    }
}
@media (max-width: 767px) {
    .table-responsive--sm thead {
        display: none;
    }
    .table-responsive--sm tbody tr:nth-child(odd) {
        background-color: aliceblue;
    }
    .table-responsive--sm tr th, .table-responsive--sm tr td {
        display: block;
        padding-left: 40% !important;
        text-align: right !important;
        border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
    }
    .table-responsive--sm tr th:first-child, .table-responsive--sm tr td:first-child {
        border-top: none !important;
    }
    .table-responsive--sm [data-label]::before {
        display: block;
    }
}
@media (max-width: 1199px) {
    *[class*="table-responsive--"].data-label--none tr th, *[class*="table-responsive--"].data-label--none tr td {
        padding-left: .75rem;
    }
}
/* table css end */
/* form css start */
.form-group {
    margin-bottom: 1.25rem;
}
.form--control {
    padding: 0.625rem 1.25rem;
    border: 1px solid #cacaca;
    width: 100%;
    background-color: #fff;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    color: #000;
    height: 3.125rem;
}
.form--control:focus{
    border-color: #bfbcbc !important;
    transition: all .3s;
}
.form--control[readonly], .form--control[disabled]{
    background-color: #f5f5f5;
    cursor: no-drop;
}
.form--control.style--two {
    border-width: 0 0 1px 0;
    padding: 0.625rem 0;
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
    font-family: "Maven Pro", sans-serif;
    border-bottom-color: #999999;
}
.form--control.style--two:focus {
    box-shadow: none;
}

.input-group.border-bottom {
    border-bottom: 1px solid #999999!important;
}

.custom--form-group {
    position: relative;
}

.input-group i {
    font-size: 18px !important;
    margin-bottom: 0 !important;
}

.input-group i[class*="la"] {
    font-size: 22px !important;
}

.custom--form-group .form--control {
    border-color: #cacaca;
    border-width: 0 0 1px 0;
    padding: 0.625rem 0;
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
    position: relative;
    z-index: 2;
    background-color: transparent;
}
.custom--form-group .form--control:focus, .custom--form-group .form--control.hascontent {
    box-shadow: none;
}
.custom--form-group .form--control:focus ~ label, .custom--form-group .form--control.hascontent ~ label {
    top: -5px;
    font-size: 0.75rem;
}
.custom--form-group .form--control ~ label {
    color: #535353;
    font-size: 0.8125rem;
    font-weight: 400;
    position: absolute;
    top: 15px;
    left: 0;
    z-index: 1;
}
.select {
    padding: 0.625rem 1.25rem;
    width: 100%;
    border: 1px solid #cacaca;
    cursor: pointer;
    color: #464646;
    background-color: #fff;
    height: 3.125rem;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
}
.select option {
    padding: 0.625rem 0;
    display: block;
    border-top: 1px solid #e5e5e5;
}
.select.style--trans {
    background-color: transparent;
    color: #fff;
    border-color: rgba(255, 255, 255, 0.5);
}
.select.style--trans option {
    color: #363636;
}
.select.select-sm {
    height: 1.875rem;
    font-size: 0.875rem;
    padding: 0.3125rem;
}
textarea {
    min-height: 9.375rem !important;
    resize: none;
    width: 100%;
}
label {
    color: #535353;
    margin-bottom: 0.625rem;
    font-family: "Maven Pro", sans-serif;
    font-size: 0.8125rem;
    font-weight: 500;
}
.input-group > .form--control,
.input-group > .select {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}
.custom-radio {
    position: relative;
}
.custom-radio input[type=radio] {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    visibility: hidden;
    cursor: pointer;
}
.custom-radio input[type=radio]:checked ~ label::before {
    border-width: 2px;
    border-color: #4582ff;
}
.custom-radio input[type=radio]:checked ~ label::after {
    opacity: 1;
}
.custom-radio label {
    margin-bottom: 0;
    position: relative;
    padding-left: 20px;
}
.custom-radio label::before {
    position: absolute;
    content: '';
    top: 3px;
    left: 0;
    width: 15px;
    height: 15px;
    border: 1px solid #888888;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.custom-radio label::after {
    position: absolute;
    content: '';
    top: 7px;
    left: 4px;
    width: 7px;
    height: 7px;
    background-color: #4582ff;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    opacity: 0;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.wrong-info .form--control {
    border-color: #ea5455 !important;
    box-shadow: 0 0 6px 1px rgba(234, 84, 85, 0.3) !important;
}
/* form css end*/
/* card css start */
.custom--card, .d-widget {
    background-color: #fff;
    box-shadow: none;
    border: 1px solid #d6d6d6;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.custom--card.border-0 .card-header {
    border-bottom: none !important;
}
.custom--card  .accordion-item:first-of-type .accordion-button {
    border-radius: 0 !important;
}

@media (max-width: 1399px) {
    .main-balance {
        font-size: 26px;
    }
} 

@media (max-width: 1199px) {
    .main-balance {
        font-size: 23px;
    }
} 


.custom--card .card-header, .d-widget .card-header {
    background-color: #4582ff0a;
}
.custom--card .card-body, .d-widget .card-body {
    padding: 1.25rem;
}
.card {
    border-radius: 8px;
    overflow: hidden;
    border-width: 1px;
}
.card-title {
    margin-bottom: 0;
}
.card.style--two {
    background-color: transparent;
}
.card.style--two .card-header {
    padding: 0.9375rem 1.5625rem;
    background-color: transparent;
}
/* card css end */
/* modal css start */
.modal {
    z-index: 999999;
}
/* modal css end */
/* header start */
.header {
    background-color: #002046;
}
.header__top {
    background-color: #4582ff;
    padding: 0.3125rem 0;
}
.header__bottom {
    padding: 0.3125rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}
@media (max-width: 1199px) {
    .header__bottom {
        padding: 0.625rem 0;
    }
}
.header .site-logo img {
    width: 10.9375rem;
}
@media (max-width: 1199px) {
    .header .site-logo img {
        max-width: 9.375rem;
    }
}
.header .main-menu {
    margin-left: 4.375rem;
}
@media (max-width: 1199px) {
    .header .main-menu {
        margin-left: 0;
        padding: 0.9375rem 0;
    }
}
.header .main-menu li {
    position: relative;
}
@media (max-width: 1199px) {
    .header .main-menu li {
        border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    }
}
.header .main-menu li:last-child a {
    padding-right: 0;
}
.header .main-menu li.menu_has_children {
    position: relative;
}
.header .main-menu li.menu_has_children.open .sub-menu {
    display: block;
}
.header .main-menu li.menu_has_children > a {
    padding-right: 1.5625rem;
}
@media (max-width: 1199px) {
    .header .main-menu li.menu_has_children > a {
        display: block;
    }
}
.header .main-menu li.menu_has_children > a::before {
    position: absolute;
    content: "\f067";
    font-family: "Line Awesome Free";
    font-weight: 900;
    top: 1rem;
    right: 0;
    color: #ffffff;
}
@media (max-width: 1199px) {
    .header .main-menu li.menu_has_children > a::before {
        display: block;
        top: 0.5625rem;
    }
}
.header .main-menu li.menu_has_children:hover > a::before {
    content: "\f068";
    color: #4582ff;
}
.header .main-menu li a {
    padding: 0.9375rem 0.9375rem 0.9375rem 0;
    text-transform: capitalize;
    font-size: 0.9375rem;
    color: #ffffff;
    position: relative;
    font-weight: 400;
}
@media (max-width: 1399px) {
    .header .main-menu li a {
        font-size: 0.8rem;
    }
}
@media (max-width: 1199px) {
    .header .main-menu li a {
        color: #ffffff;
        padding: 0.5rem 0;
        display: block;
    }
}
.header .main-menu li a:hover, .header .main-menu li a:focus {
    color: #4582ff;
}
.header .main-menu li .sub-menu {
    position: absolute;
    width: 220px;
    top: 105%;
    left: 0;
    z-index: 9999;
    background-color: #fff;
    padding: 0.625rem 0;
    -webkit-box-shadow: 0px 18px 54px -8px rgba(0, 0, 0, 0.15);
    box-shadow: 0px 5px 25px 2px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    opacity: 0;
    visibility: hidden;
    border: 2px solid #e5e5e5;
}
.header .main-menu li .sub-menu::before {
    position: absolute;
    content: '';
    top: -19px;
    left: 20px;
    border-width: 10px 10px 10px 10px;
    border-style: solid;
    border-color: transparent transparent #fff transparent;
}
@media (max-width: 1199px) {
    .header .main-menu li .sub-menu {
        opacity: 1;
        visibility: visible;
        display: none;
        position: static;
        -webkit-transition: none;
        -o-transition: none;
        transition: none;
        width: 100%;
        background-color: transparent;
        border: none;
        padding-left: 20px;
    }
    .header .main-menu li .sub-menu::before {
        display: none;
    }
}
.header .main-menu li .sub-menu li {
    border-bottom: 1px dashed #e5e5e5;
}

@media (max-width: 1199px) {
    .header .main-menu li .sub-menu li {
        border-bottom: 1px dashed #e5e5e530;
    }
}

.header .main-menu li .sub-menu li:last-child {
    border-bottom: none;
}
.header .main-menu li .sub-menu li a {
    padding: 0.5rem 1.5625rem;
    display: block;
    color: #464646;
    position: relative;
    font-size: 0.875rem;
}
@media (max-width: 1199px) {
    .header .main-menu li .sub-menu li a {
        color: #c7c7c7;
        padding-left: 0;
    }
}

.header .main-menu li .sub-menu li a::before {
    position: absolute;
    content: '';
    top: 0;
    left: -0.25rem;
    width: 0.25rem;
    height: 100%;
    background-color: #4582ff;
    opacity: 0;
}
.header .main-menu li .sub-menu li a:hover {
    background-color: rgba(69, 130, 255, 0.05);
    color: #4582ff;
}
.header .main-menu li .sub-menu li a:hover::before {
    opacity: 1;
    left: 0;
}
.header .main-menu li .sub-menu li + li {
    margin-left: 0;
}
.header .main-menu li:hover .sub-menu {
    top: 100%;
    opacity: 1;
    visibility: visible;
}
.header .main-menu li + li {
    margin-left: 1.25rem;
}
@media (max-width: 1199px) {
    .header .main-menu li + li {
        margin-left: 0;
    }
}
.header .nav-right {
    padding-left: 3.125rem;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
}
@media (max-width: 1199px) {
    .header .nav-right {
        padding-left: 0;
    }
}
.header .nav-right .select {
    background-color: transparent;
    width: auto;
    padding: 0;
    height: auto;
    color: #ffffff;
    border: none;
}
.header .nav-right .select option {
    color: #ffffff;
    background-color: #002046;
}
.header.style--two {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9;
    background-color: transparent;
}
.header.style--two.menu-fixed .header__top {
    display: none;
}
.header.style--two.menu-fixed .header__bottom {
    background-color: #002046;
}
.header.style--two .header__top {
    padding: 10px 3.125rem;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}
.header.style--two .header__bottom {
    padding: 0 3.125rem;
    background-color: rgba(0, 32, 70, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: none;
}
.header.style--two .main-menu li a {
    font-family: "Maven Pro", sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    padding: 1.875rem 0.9375rem 1.875rem 0;
    font-size: 1rem;
}
.header.style--two .main-menu li.menu_has_children > a::before {
    top: 30px;
}
.header.style--two .main-menu li .sub-menu {
    left: auto;
    right: 0;
}
.header.style--two .main-menu li .sub-menu::before {
    left: auto;
    right: 20px;
}
.header.style--two .main-menu li .sub-menu li a {
    font-size: 0.9375rem;
    text-transform: capitalize;
}
.header-info-list {
    margin: -0.1875rem -0.625rem;
}
.header-info-list li {
    padding: 0.1875rem 0.625rem;
}
.header-info-list li a {
    font-size: 0.875rem;
    color: #fff;
}
.header-info-list li a i {
    font-size: 18px;
}
.header-info-list li a:hover {
    color: #4582ff;
}
.social-links {
    margin: -0.1875rem -0.3125rem;
}
.social-links li {
    margin: 0.1875rem 0.3125rem;
}
.social-links.style--white li a {
    color: #fff;
}
.header-top-menu {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    margin: -0.3125rem -0.4375rem;
    padding-right: 20px;
    margin-right: 20px;
    position: relative;
}
@media (max-width: 575px) {
    .header-top-menu {
        margin-right: 10px;
        padding-right: 10px;
    }
}
.header-top-menu::after {
    position: absolute;
    content: '';
    top: 50%;
    right: 0;
    width: 1px;
    height: 16px;
    margin-top: -8px;
    background-color: rgba(255, 255, 255, 0.8);
}
.header-top-menu li {
    margin: 0.3125rem 0.4375rem;
}
.header-top-menu li a {
    font-size: 0.875rem;
    color: #fff;
}
.header-username {
    color: #fff;
}
.header-username:hover {
    color: #fff;
}
.header-user {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    position: relative;
    cursor: pointer;
}

.header-user .thumb {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    overflow: hidden;
    background-color: #000;
    margin-right: 0.5rem;
}
.header-user .name {
    font-size: 0.875rem;
    color: #fff;
    font-weight: 600;
}
.header-user .header-user-menu {
    position: absolute;
    top: 110%;
    right: 0;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: .1s linear;
    display: none;
}
.header-user:hover .header-user-menu {
    opacity: 1;
    visibility: visible;
    top: 100%;
    display: block;
}
.header-user .header-user-menu::before {
    position: absolute;
    content: '';
    top: -19px;
    right: 20px;
    border-width: 10px 10px 10px 10px;
    border-style: solid;
    border-color: transparent transparent #fff transparent;
}
.header-user-menu {
    width: 200px;
    background-color: #fff;
    box-shadow: 0 5px 10px 1px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.header-user-menu li {
    border-bottom: 1px dashed #e5e5e5;
}
.header-user-menu li:last-child {
    border-bottom: none;
}
.header-user-menu li a {
    color: #464646;
    padding: 5px 20px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
}
.header-user-menu li a:hover {
    color: #4582ff;
    background-color: rgba(69, 130, 255, 0.05);
}
.header-user-menu li a i {
    font-size: 1.125rem;
    margin-right: 0.5rem;
}
.header-login-btn {
    color: #fff;
    padding: 0.5rem 1.25rem;
    border: 1px solid #4582ff;
    background-color: #4582ff;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    line-height: 1;
}
.header-login-btn:hover {
    background-color: rgba(69, 130, 255, 0.35);
    box-shadow: 0 0 5px 2px rgba(69, 130, 255, 0.5);
}
.header-login-btn i {
    color: #fff;
    font-size: 1.5rem;
    margin-right: 0.375rem;
}
@media (max-width: 1199px) {
    .navbar-collapse {
        background-color: #071e3e;
        padding: 0 1.875rem 1.25rem 1.875rem;
    }
}

.navbar-toggler {
    padding: 0;
    border: none;
}
.navbar-toggler:focus {
    outline: none;
    box-shadow: none;
}
.menu-toggle {
    margin: 10px 0;
    position: relative;
    display: block;
    width: 2.1875rem;
    height: 1.25rem;
    cursor: pointer;
    background: transparent;
    border-top: 2px solid;
    border-bottom: 2px solid;
    color: #ffffff;
    font-size: 0;
    -webkit-transition: all 0.25s ease-in-out;
    -o-transition: all 0.25s ease-in-out;
    transition: all 0.25s ease-in-out;
    cursor: pointer;
}
.menu-toggle:before, .menu-toggle:after {
    content: '';
    display: block;
    width: 100%;
    height: 2px;
    position: absolute;
    top: 50%;
    left: 50%;
    background: currentColor;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    transition: -webkit-transform 0.25s ease-in-out;
    -webkit-transition: -webkit-transform 0.25s ease-in-out;
    -o-transition: -webkit-transform 0.25s ease-in-out;
    transition: transform 0.25s ease-in-out;
    -moz-transition: -webkit-transform 0.25s ease-in-out;
    -ms-transition: -webkit-transform 0.25s ease-in-out;
}
@media (max-width: 1199px) {
    .menu-toggle:before, .menu-toggle:after {
        background-color: #ffffff;
    }
}
.navbar-toggler[aria-expanded="true"] span {
    border-color: transparent;
}
.navbar-toggler[aria-expanded="true"] span:before {
    -webkit-transform: translate(-50%, -50%) rotate(45deg);
    -ms-transform: translate(-50%, -50%) rotate(45deg);
    transform: translate(-50%, -50%) rotate(45deg);
}
.navbar-toggler[aria-expanded="true"] span:after {
    -webkit-transform: translate(-50%, -50%) rotate(-45deg);
    -ms-transform: translate(-50%, -50%) rotate(-45deg);
    transform: translate(-50%, -50%) rotate(-45deg);
}
.navbar-toggler[aria-expanded="true"] span.menu-toggle:hover {
    color: #ffffff;
}
@media (max-width: 1199px) {
    .navbar-toggler[aria-expanded="true"] span.menu-toggle:hover {
        color: #ffffff;
    }
}
.navbar-toggler[aria-expanded="true"] span {
    border-color: transparent;
}
.navbar-toggler[aria-expanded="true"] span:before {
    -webkit-transform: translate(-50%, -50%) rotate(45deg);
    -ms-transform: translate(-50%, -50%) rotate(45deg);
    transform: translate(-50%, -50%) rotate(45deg);
}
.navbar-toggler[aria-expanded="true"] span:after {
    -webkit-transform: translate(-50%, -50%) rotate(-45deg);
    -ms-transform: translate(-50%, -50%) rotate(-45deg);
    transform: translate(-50%, -50%) rotate(-45deg);
}
.animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}
.fadeInDown {
    -webkit-animation-name: fadeInDown;
    animation-name: fadeInDown;
}
@-webkit-keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-20px);
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@-moz-keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-20px);
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@-ms-keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-20px);
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-20px);
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
/* header end */
/* hero section css start */
.hero {
    padding-top: 18.75rem;
    padding-bottom: 15.625rem;
    position: relative;
    z-index: 1;
}
.hero::after {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #bdc3c7;
    background: -webkit-linear-gradient(to right, #002046, rgba(0, 32, 70, 0.15));
    background: linear-gradient(to right, #002046, rgba(0, 32, 70, 0.15));
    opacity: 0.45;
    z-index: -1;
}
.hero__subtitle {
    color: #fff;
    position: relative;
    padding-left: 35px;
}
.hero__subtitle::before {
    position: absolute;
    content: '';
    top: 50%;
    left: 0;
    width: 25px;
    height: 1px;
    background-color: #4582ff;
}
.hero__title {
    font-size: 4.125rem;
    color: #fff;
}
.hero__des {
    font-size: 1.125rem;
    color: #fff;
}
/* hero section css end */
/* brand-section css start */
.brand-section {
    padding: 2.1875rem 0;
}
.brand-slider .slick-track {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
}
.brand-item img {
    display: inline-block;
}
/* brand-section css end */
/* about section css start */
.about-thumb {
    float: left;
    width: 140%;
    position: relative;
    margin-bottom: 1.875rem;
}
.about-thumb img {
    width: 100%;
}
.about-thumb .about-img-content {
    padding: 1.25rem 3.125rem;
    background-color: #4582ff;
    color: #fff;
    display: inline-block;
    text-align: center;
    position: absolute;
    bottom: -1.875rem;
    left: -5rem;
    z-index: 1;
}
.about-thumb .about-img-content::after {
    position: absolute;
    content: '';
    background: transparent;
    background-image: radial-gradient(#fff 20%, transparent 0), radial-gradient(#fff 20%, transparent 0);
    background-size: 10px 10px;
    background-position: 0 0, 25px 25px;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.15;
    z-index: -1;
}
.about-thumb .about-img-content .years {
    font-size: 4.5rem;
    display: block;
    color: #fff;
}
.about-thumb .about-img-content .caption {
    font-size: 1.375rem;
}
.award-list {
    margin: -0.4375rem -0.625rem;
}
.award-list li {
    margin: 0.4375rem 0.625rem;
}
.award-list li img {
    max-width: 6.25rem;
}
/* about section css end  */
/* d-sidebar css start */
.d-sidebar {
    background-color: #002046;
    position: relative;
    z-index: 1;
}
.d-sidebar::after {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #002046 21px, transparent 1%) center, linear-gradient(#002046 21px, transparent 1%) center, #a799cc;
    background-size: 22px 22px;
    opacity: 0.4;
    z-index: -1;
}
.d-sidebar.rounded {
    border-radius: 8px !important;
    -webkit-border-radius: 8px !important;
    -moz-border-radius: 8px !important;
    -ms-border-radius: 8px !important;
    -o-border-radius: 8px !important;
}
.d-sidebar.rounded .sidebar-menu .sidebar-menu__link {
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.d-sidebar .slimScrollBar {
    background-color: #fff !important;
    width: 4px !important;
}
.d-sidebar .header-username {
    height: 52px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    padding: 0.3125rem 0.9375rem;
}
.d-sidebar .sidebar-close-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 30px;
    display: none;
}
@media (max-width: 991px) {
    .d-sidebar .sidebar-close-btn {
        display: inline-block;
    }
}
.sidebar-menu-wrapper {
    padding: 0.9375rem;
}
.sidebar-menu__item {
    margin: 0.1875rem 0;
}
.sidebar-menu__item.active .sidebar-menu__link {
    background-color: rgba(255, 255, 255, 0.1);
    color: #4582ff;
    border-color: #4582ff;
    z-index: 1;
}
.sidebar-menu__item.sidebar-dropdown {
    position: relative;
}
.sidebar-menu__item.sidebar-dropdown::after {
    position: absolute;
    content: "\f078";
    top: 15px;
    right: 10px;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: #f1f1f1;
    font-size: 0.6875rem;
    line-height: 1;
    z-index: -1;
}
.sidebar-menu__item.sidebar-dropdown .sidebar-menu__link.side-menu--open {
    background-color: rgba(255, 255, 255, 0.1);
    color: #4582ff;
    border-color: #4582ff;
}
.sidebar-menu__link {
    padding: 0.625rem 0.9375rem;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    color: #d6d6d6;
    font-size: 0.875rem;
    border-left: 3px solid transparent;
}
.sidebar-menu__link:hover {
    color: #4582ff;
}
.sidebar-menu__link i {
    font-size: 1.375rem;
    margin-right: 8px;
}
.sidebar-menu__header {
    padding: 0 0.9375rem;
    color: #9c9c9c;
    font-family: "Maven Pro", sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.8125rem;
    margin-bottom: 0.625rem;
    position: relative;
    z-index: 1;
}
.sidebar-menu__header::before {
    position: absolute;
    content: '';
    top: 10px;
    left: 0;
    width: 10px;
    height: 2px;
    background-color: #4582ff;
    opacity: 0.8;
    z-index: -1;
}
.sidebar-menu .sidebar-menu__item ~ .sidebar-menu__header {
    margin-top: 1.875rem;
}
.sidebar-menu .sidebar-menu__header ~ .sidebar-menu__item.active {
    margin-top: 0.9375rem;
}
.sidebar-submenu {
    display: none;
}
.sidebar-submenu ul {
    padding-left: 2.75rem;
}
.sidebar-submenu ul li {
    position: relative;
}
.sidebar-submenu ul li::before {
    position: absolute;
    content: '';
    top: 50%;
    left: -10px;
    width: 5px;
    height: 1px;
    background-color: #d6d6d6;
}
.sidebar-submenu ul li a {
    padding: 0.3125rem 0;
    color: #d6d6d6;
    font-size: 0.875rem;
}
.sidebar-submenu ul li a:hover {
    color: #4582ff;
}
.sidebar-open-btn {
    background-color: transparent;
    color: #fff;
    padding: 0;
    font-size: 1.5rem;
    line-height: 1;
    display: none;
}
@media (max-width: 991px) {
    .sidebar-open-btn {
        display: inline-block;
    }
}
.accordion-button {
    flex-wrap: wrap;
}
@media (max-width: 575px) {
    .transaction-item {
        flex-wrap: wrap;
    }
}
@media (max-width: 767px) {
    .transaction-item .icon-wrapper .trans-title {
        font-size: 0.875rem;
    }
}
@media (max-width: 767px) {
    .transaction-item .icon-wrapper p, .transaction-item .icon-wrapper span {
        font-size: 0.75rem !important;
    }
}
@media (max-width: 575px) {
    .transaction-item .content-wrapper {
        padding-left: 2.5rem;
    }
}
@media (max-width: 767px) {
    .transaction-item .content-wrapper p {
        font-size: 0.75rem !important;
    }
}
@media (max-width: 767px) {
    .transaction-item .amount-wrapper p {
        font-size: 0.8125rem !important;
    }
}
@media (max-width: 767px) {
    .transaction-item .amount-wrapper span {
        font-size: 0.75rem !important;
    }
}
/* d-sidebar css end */
/* dashboard css start*/
.d-widget {
    background-color: #fff;
    border: none;
    border-left: 4px solid #4582ff;
    position: relative;
    box-shadow: 0 3px 6px #0000000d;
}
.d-widget.curve--shape {
    z-index: 1;
}
.d-widget.curve--shape::before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #4582ff;
    opacity: 0.04;
    -webkit-clip-path: polygon(25% 73%, 47% 52%, 73% 57%, 100% 16%, 100% 100%, 0 100%, 0 79%);
    clip-path: polygon(25% 73%, 47% 52%, 73% 57%, 100% 16%, 100% 100%, 0 100%, 0 79%);
    z-index: -1;
}
.d-widget:hover {
    box-shadow: 0 5px 15px 0 rgba(60, 60, 60, 0.121569);
}
.d-widget__amount {
    font-size: 1.75rem;
}

.amount__responsive{
    font-size: 23px;
    font-weight: 500 !important;
}

.main__amount__responsive{
    font-size: 28px;
    font-weight: 500 !important;
}

@media (max-width: 1399px) {
    .d-widget__amount {
        font-size: 1.45rem;
    }
}

@media (max-width: 1199px) {
    .d-widget__amount {
        font-size: 1.35rem;
    }
}

.d-widget__header {
    padding: 0.9375rem 1.25rem;
    border-bottom: 1px solid #cacaca;
}
.d-widget__content {
    padding: 1.875rem 1.25rem;
}

@media (max-width: 575px) {
    .d-widget__content.px-5 {
        padding-left: 20px !important;
        padding-right: 20px !important;
    }
}

.d-widget__content i {
    font-size: 2.625rem;
    color: #464646;
    margin-bottom: 0.9375rem;
}
@media (max-width: 1199px) {
    .d-widget__content i  {
        font-size: 2.15rem;
    }
}
.d-widget__footer {
    padding: 0.625rem 1.25rem;
    border-top: 1px solid #e5e5e5;
}
.d-widget__footer a {
    color: #464646;
    display: inline-flex;
    align-items: center;
}
.d-widget__footer a:hover {
    color: #4582ff;
}
.d-widget__footer a i {
    font-size: 1.125rem;
    margin-inline-start: 5px;
}
.quick-link-card {
    color: #464646;
    text-align: center;
}
.quick-link-card:hover {
    color: #4582ff;
}
.quick-link-card:hover .icon {
    color: #fff;
    border-color: #4582ff;
}
.quick-link-card:hover .icon::before {
    width: 100%;
    height: 100%;
    margin: 0;
    top: 0;
    left: 0;
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
    opacity: 1;
}
.quick-link-card .icon {
    width: 65px;
    height: 65px;
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    font-size: 2.25rem;
    position: relative;
    z-index: 1;
    color: #4582ff;
}
.quick-link-card .icon::before {
    position: absolute;
    content: '';
    top: 50%;
    left: 50%;
    width: 1.25rem;
    height: 1.25rem;
    margin-left: -1.25rem;
    margin-top: -1.25rem;
    background-color: #4582ff;
    opacity: 0.5;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    z-index: -1;
}
.quick-link-card .caption {
    font-size: 0.8125rem;
    font-weight: 600;
    margin-top: 3px;
}

.mobile-quick-links {
    display: none;
}

@media (max-width: 991px) {
    .mobile-quick-links {
        display: block;
    }
    .mobile-quick-links .quick-link-card .caption {
        font-size: 11px;
    }
}

.transaction-top-form {
    display: none;
}
.trans-serach-open-btn {
    background-color: transparent;
    color: #4582ff;
    font-size: 1.25rem;
}
.custom-select-search-box {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    background-color: #fff;
    align-items: center;
    border: 1px solid #cacaca;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.custom-select-search-box .form--control {
    width: calc(100% - 60px);
    border: none;
}
.custom-select-search-box .form--control:focus {
    box-shadow: none;
}.custom-select-search-box .search-box-btn {
    width: 60px;
    background-color: transparent;
    color: #4582ff;
    font-size: 1.5rem;
    border-left: 1px solid #cacaca;
}
.custom-select-box-two {
    background-color: #fff;
    border: 1px solid #cacaca;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    padding: 0.125rem 0.5rem;
}
.custom-select-box-two label {
    color: #a0a0a0;
    font-size: 0.75rem;
    margin-bottom: 0;
}
.custom-select-box-two select {
    background-color: transparent;
    width: 100%;
    border: none;
    font-family: "Maven Pro", sans-serif;
    color: #373e4a;
    font-size: 0.875rem;
    font-weight: 500;
}
.table--acordion {
    background-color: #fff;
}
.table--acordion .accordion-body {
    background-color: #faebd72e;
}
.table--acordion .accordion-button {
    text-align: left;
    align-items: center;
}
.table--acordion .accordion-button::after {
    display: none;
}
.table--acordion .accordion-button:focus {
    box-shadow: none;
}
.table--acordion .left {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
}
.table--acordion .left .icon {
    width: 2.5rem;
    height: 2.5rem;
    background-color: rgba(69, 130, 255, 0.15);
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    color: #4582ff;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    font-size: 1.625rem;
}
@media (max-width: 575px) {
    .table--acordion .left .icon {
        width: 1.875rem;
        height: 1.875rem;
        font-size: 1.375rem;
    }
}
.table--acordion .left .content {
    width: calc(100% - 2.5rem);
    padding-left: 0.9375rem;
}
@media (max-width: 575px) {
    .table--acordion .left .content {
        padding-left: 0.625rem;
    }
}
.table--acordion .accordion-item.rcv-item .icon {
    background-color: rgba(40, 199, 111, 0.15);
    color: #28c76f;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}
.table--acordion .accordion-item.sent-item .icon {
    background-color: rgba(234, 84, 85, 0.15);
    color: #ea5455;
    -webkit-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
}
.d-user-notification {
    padding: 0.625rem 0.9375rem;
    background-color: #002046;
}
@media (max-width: 575px) {
    .d-user-notification {
        justify-content: center;
    }
}
.d-user-notification .icon {
    width: 3.125rem;
    border-right: 1px solid rgba(255, 255, 255, 0.5);
    font-size: 1.75rem;
    line-height: 1;
}
@media (max-width: 440px) {
    .d-user-notification .icon {
        display: none;
    }
}
.d-user-notification .content {
    width: calc(100% - (3.125rem + 7rem));
    padding-left: 0.9375rem;
    text-align: center;
}
@media (max-width: 575px) {
    .d-user-notification .content {
        width: auto;
    }
}

@media (max-width: 767px) {
    .d-user-notification .content p {
        font-size: 13px;
    }
}

.d-user-notification .right {
    width: 7rem;
}
@media (max-width: 575px) {
    .d-user-notification .right {
        width: 100%;
        margin-top: 8px;
    }
}

.security-single {
    background-color: #fff;
    padding: 0.9375rem 1.25rem;
    border: 1px solid #e5e5e5;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}
.security-single__info {
    padding-top: 1.5625rem;
    border-top: 1px solid #e5e5e5;
    margin-top: 0.9375rem;
}
.security-single__body {
    display: none;
    padding-top: 1.5625rem;
    border-top: 1px solid #e5e5e5;
    margin-top: 0.9375rem;
}
.big-icon {
    font-size: 3.875rem;
}
/* dashboard css end */
/* add bank account section css start */
.bank-icon {
    display: inline-flex;
    font-size: 3rem;
}
.bank-icon.has--plus {
    position: relative;
}
.bank-icon.has--plus::after {
    position: absolute;
    content: "\f055";
    font-family: 'Line Awesome Free';
    font-weight: 900;
    font-size: 1.125rem;
    top: 4px;
    right: 2px;
    width: 1.125rem;
    height: 1.125rem;
    background-color: #f5f7faf2;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    line-height: 1;
}
/* add bank account section css end */
/* bank list section css start */
.bank-card {
    padding: 1.25rem;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
@media (max-width: 420px) {
    .bank-card {
        text-align: center;
        justify-content: center;
    }
}
.bank-card__icon {
    width: 5.3125rem;
    height: 5.3125rem;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    font-size: calc(5.3125rem / 2);
    background-color: #4582ff;
    color: #fff;
}
.bank-card__content {
    width: calc(100% - 5.3125rem);
    padding-left: 1.25rem;
}
@media (max-width: 420px) {
    .bank-card__content {
        width: 100%;
        padding-left: 0;
        margin-top: 1.25rem;
    }
}
.bank-card.approved .bank-card__icon {
    position: relative;
}
.bank-card.approved .bank-card__icon::after {
    position: absolute;
    content: "\f00c";
    font-family: 'Line Awesome Free';
    font-weight: 900;
    top: 0;
    right: 0;
    width: 25px;
    height: 25px;
    line-height: 1;
    font-size: 12px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background-color: #28c76f;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}
.bank-card.approved.warning .bank-card__icon::after  {
    content: "\f05a";
    background-color: #ff9f43;
}
.bank-card.add-bank {
    background-color: transparent;
    box-shadow: none;
    border: 2px dashed #8a8a8a;
}
.bank-card.add-bank:hover {
    background-color: rgba(211, 220, 233, 0.94902);
}
.bank-card.add-bank .bank-card__icon {
    background-color: transparent;
    border: 1px solid #626f85;
    color: #373e4a;
    position: relative;
}
.bank-card.add-bank .bank-card__icon::after {
    position: absolute;
    content: "\f067";
    font-family: 'Line Awesome Free';
    font-weight: 900;
    top: 0;
    right: 0;
    width: 25px;
    height: 25px;
    line-height: 1;
    font-size: 12px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f7faf2;
    border: 1px solid #979797;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}
.modal-bank-details {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.modal-bank-details .left {
    width: 40%;
    padding: 1.25rem;
    background-color: #fafafa;
}
@media (max-width: 991px) {
    .modal-bank-details .left {
        width: 100%;
    }
}
.modal-bank-details .left .icon {
    width: 5.9375rem;
    height: 5.9375rem;
    border: 1px solid #cacaca;
    font-size: calc(5.9375rem / 2);
}
.modal-bank-details .right {
    width: 60% !important;
    padding-left: 20px;
}
@media (max-width: 991px) {
    .modal-bank-details .right {
        width: 100% !important;
        padding-left: 0;
        margin-top: 1.5625rem;
    }
}
@media (max-width: 991px) {
    .modal-bank-details .slimScrollDiv {
        width: 100% !important;
    }
}
/* bank list section css end */
/* deposit section css start */
.deposit-card {
    padding: 0.625rem;
    background-color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid #cacaca;
    height: 100%;
}
.deposit-card .thumb {
    width: 32%;
}
@media (max-width: 1199px) and (min-width: 576px) {
    .deposit-card .thumb {
        width: 100%;
    }
}
@media (max-width: 380px) {
    .deposit-card .thumb {
        width: 100%;
    }
}
.deposit-card .content {
    width: 68%;
    padding-left: 1.25rem;
}
@media (max-width: 1199px) and (min-width: 576px) {
    .deposit-card .content {
        width: 100%;
        padding-left: 0;
        text-align: center;
        padding: 0.9375rem 0.9375rem;
        margin-top: 0.9375rem;
    }
}
@media (max-width: 380px) {
    .deposit-card .content {
        width: 100%;
        padding-left: 0;
        text-align: center;
        padding: 0.9375rem 0.9375rem;
        margin-top: 0.9375rem;
    }
}
.deposit-card .content .title {
    font-size: 1.25rem;
    margin-bottom: 0.625rem;
}
@media (max-width: 1399px) {
    .deposit-card .content .title {
        font-size: 1.125rem;
    }
}
.deposit-card ul li {
    padding: 0.375rem 0.9375rem;
    font-size: 0.875rem;
}
.deposit-card ul li:nth-child(even) {
    background-color: #EBF5F5;
}
/* deposit section css end */
/* agent dashboard css start */
.agent-dashboard .d-sidebar {
    width: 250px;
    position: fixed;
    z-index: 99;
    left: 0;
    top: 0;
    border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    -ms-border-radius: 0 !important;
    -o-border-radius: 0 !important;
}
.agent-dashboard .d-sidebar.rounded {
    border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    -ms-border-radius: 0 !important;
    -o-border-radius: 0 !important;
}
@media (max-width: 991px) {
    .agent-dashboard .d-sidebar {
        left: -250px;
    }
}
.agent-dashboard .d-sidebar.active {
    left: 0;
}
.agent-dashboard .agent-dashboard__body {
    margin-left: calc(250px + 1.875rem);
    padding-bottom: 3.125rem;
    padding-right: 1.875rem;
}
@media (max-width: 991px) {
    .agent-dashboard .agent-dashboard__body {
        margin-left: 1.875rem;
    }
}
@media (max-width: 767px) {
    .agent-dashboard .agent-dashboard__body {
        padding-right: 0.9375rem;
        margin-left: 0.9375rem;
    }
}
.agent-dashboard .dashboard-top-nav {
    margin-left: 250px;
    background-color: #002046;
    padding: 0.625rem 1.25rem;
}
@media (max-width: 991px) {
    .agent-dashboard .dashboard-top-nav {
        margin-left: 0;
    }
}
/* agent dashboard css end */
/* doc-header css start */
.doc-navbar {
    background-color: #002046;
}
.doc-navbar .navbar-brand img {
    max-width: 120px;
}
@media (max-width: 1199px) {
    .doc-navbar .navbar-collapse {
        padding: 0;
        background-color: transparent;
    }
}
@media (max-width: 767px) {
    .doc-navbar .navbar-collapse {
        margin-top: 1.25rem;
    }
}
.doc-navbar .navbar-toggler {
    color: #fff;
    font-size: 1.5rem;
}
@media (max-width: 767px) {
    .doc-navbar .nav-item {
        border-top: 1px solid rgba(255, 255, 255, 0.15);
    }
}
.doc-navbar .nav-item .nav-link {
    color: #fff;
}
.doc-navbar .nav-item .nav-link:hover {
    color: #4582ff;
}
.documentation-section p, .documentation-section span, .documentation-section li, .documentation-section a {
    font-size: 0.875rem;
}
.sidebar-menu-open-btn {
    padding: 0.375rem 1.25rem;
    background-color: #fff;
    border: 1px solid #e5e5e5;
    box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.05);
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    display: none;
}
@media (max-width: 991px) {
    .sidebar-menu-open-btn {
        display: inline-block;
    }
}
.documentation-menu-wrapper {
    position: sticky;
    top: 50px;
}
@media (max-width: 991px) {
    .documentation-menu-wrapper {
        position: fixed;
        height: 100vh;
        top: 0;
        left: -255px;
        width: 250px;
        box-shadow: 5px 0 5px rgba(0, 0, 0, 0.15);
        background-color: #fff;
        padding: 1.25rem;
        z-index: 9;
        -webkit-transition: all 0.3s;
        -o-transition: all 0.3s;
        transition: all 0.3s;
    }
}
.documentation-menu-wrapper.open {
    left: 0;
}
.documentation-menu-wrapper .sidebar-close-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 30px;
    background-color: #4582ff;
    color: #fff;
    display: none;
}
@media (max-width: 991px) {
    .documentation-menu-wrapper .sidebar-close-btn {
        display: inline-block;
    }
}
.sidebar-menu .menu li.has_child {
    margin-bottom: 20px;
}
.sidebar-menu .menu li.has_child > a {
    font-family: "Maven Pro", sans-serif;
    color: #0c0d0f;
    position: relative;
}
.sidebar-menu .menu li.has_child > a::before {
    position: absolute;
    content: '';
    top: 50%;
    left: -14px;
    margin-top: -1px;
    width: 10px;
    height: 2px;
    background-color: #21252d;
}
.sidebar-menu .menu li a {
    color: #464646;
}
.sidebar-menu .menu li a:hover {
    color: #4582ff;
}
.sidebar-menu .drp-menu {
    margin-top: 0.3125rem;
    padding-left: 0.9375rem;
    border-left: 1px solid #cacaca;
}
.sidebar-menu .drp-menu li.active {
    position: relative;
}
.sidebar-menu .drp-menu li.active::before {
    position: absolute;
    content: '';
    top: 9px;
    left: -17px;
    width: 3px;
    height: 50%;
    background-color: #4582ff;
}
.sidebar-menu .drp-menu li.active > a {
    color: #4582ff;
}
.sidebar-menu .drp-menu li a {
    padding: 0.1875rem 0;
}
.doc-body {
    padding-left: 1.875rem;
    border-left: 1px solid #cacaca;
}
@media (max-width: 991px) {
    .doc-body {
        padding-left: 0;
        border-left: none;
    }
}
.doc-section + .doc-section {
    margin-top: 6.25rem;
}
@media (max-width: 767px) {
    .doc-section + .doc-section {
        margin-top: 4.375rem;
    }
}
.doc-section {
    display: flex;
    flex-wrap: wrap;
}
.doc-section .doc-content {
    width: 60%;
}
@media (max-width: 767px) {
    .doc-section .doc-content {
        width: 100%;
    }
}
.doc-section .doc-content section {
    margin-bottom: 30px;
    background-color: transparent;
}
.plugin-item {
  display: flex;
  border: 1px solid rgb(0, 0, 0, 0.1);
  border-radius: 5px;
  align-items: center;
  padding: 10px 15px;
  margin-bottom: 15px;
}
.plugin-item__thumb {
    border-radius: 5px;
    overflow: hidden;
}
.plugin-item__thumb img {
    max-width: 100px;
    object-fit: cover;
}
.plugin-item__content {
    width: calc( 100% - 100px);
    padding-left: 20px;
}
.plugin-item__content img{
    max-height: 50px;
    margin-top: 8px;
}
.plugin-item__title {
    margin-bottom: 5px !important;
}
.plugin-item__button {
    background-color: white;
}
.doc-section p {
    margin-bottom: 0.9375rem;
}
.doc-section h1, .doc-section h2, .doc-section h3, .doc-section h4, .doc-section h5, .doc-section h6 {
    margin-bottom: 15px;
}
.doc-section ul {
    margin-bottom: 1.875rem;
}
.doc-section .doc-code {
    width: 40%;
    padding-left: 1.875rem;
}
@media (max-width: 767px) {
    .doc-section .doc-code {
        width: 100%;
        padding-left: 0;
    }
}
.doc-section .doc-code .doc-code-inner {
    position: sticky;
    top: 50px;
}
.doc-section .table {
    background-color: #fff;
}
.doc-section .table thead tr {
    border: none;
}
.doc-section .table th {
    white-space: nowrap;
    font-size: 0.8125rem;
    padding: 0.5rem 0.9375rem;
    background-color: #1c3147;
    color: #fff;
}
.doc-section .table th:first-child {
    border-radius: 5px 0 0 0;
    -webkit-border-radius: 5px 0 0 0;
    -moz-border-radius: 5px 0 0 0;
    -ms-border-radius: 5px 0 0 0;
    -o-border-radius: 5px 0 0 0;
}
.doc-section .table th:last-child {
    border-radius: 0 5px 0 0;
    -webkit-border-radius: 0 5px 0 0;
    -moz-border-radius: 0 5px 0 0;
    -ms-border-radius: 0 5px 0 0;
    -o-border-radius: 0 5px 0 0;
}
.doc-section .table tbody {
    font-size: 0.875rem;
}
.doc-section .table tbody tr:nth-child(even) {
    background-color: #f5fafd;
}
.doc-section .table tbody td {
    padding: 0.625rem 0.9375rem;
}
.callout {
    background-color: #f2f5f7;
    padding: 0.9375rem;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    box-shadow: 0 1px 1px #00000008;
    margin: 1.875rem 0;
    border: 1px solid rgba(0, 0, 0, 0.06);
}
.callout .callout-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.375rem;
}
.callout .callout-header i {
    font-size: 1.5rem;
    margin-right: 0.3125rem;
}
.callout .callout-header .title {
    font-size: 1rem;
    margin-bottom: 0;
}
.callout--success {
    background: #ceffe4;
    border: 1px solid #9bffc8;
}
.callout--info {
    background: #d0eeff;
    border: 1px solid #b0eaff;
}
.callout--warning {
    background: #ffedd6;
    border: 1px solid #ffd6d7;
}
.callout--danger {
    background-color: #ffe9e9;
    border: 1px solid #ffa2a24f;
}
.code-block {
    position: relative;
}
.code-block .clipboard-btn {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #1c3147;
    color: #fff;
    font-size: 0.75rem;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
}
.code-block-header {
    padding: 0.5rem 1.25rem;
    background-color: #314459;
    color: #fff;
    border-radius: 8px 8px 0 0;
    -webkit-border-radius: 8px 8px 0 0;
    -moz-border-radius: 8px 8px 0 0;
    -ms-border-radius: 8px 8px 0 0;
    -o-border-radius: 8px 8px 0 0;
}
.hljs {
    display: block;
    padding: 0.5em;
    color: #abb2bf;
    background: #1c3147;
    overflow-x: auto;
}
.hljs-comment, .hljs-quote {
    color: #5c6370;
    font-style: italic;
}
.hljs-doctag, .hljs-keyword, .hljs-formula {
    color: #c678dd;
}
.hljs-section, .hljs-name, .hljs-selector-tag, .hljs-deletion, .hljs-subst {
    color: #e06c75;
}
.hljs-literal {
    color: #56b6c2;
}
.hljs-string, .hljs-regexp, .hljs-addition, .hljs-attribute, .hljs-meta-string {
    color: #98c379;
}
.hljs-built_in, .hljs-class .hljs-title {
    color: #e6c07b;
}
.hljs-attr, .hljs-variable, .hljs-template-variable, .hljs-type, .hljs-selector-class, .hljs-selector-attr, .hljs-selector-pseudo, .hljs-number {
    color: #d19a66;
}
.hljs-symbol, .hljs-bullet, .hljs-link, .hljs-meta, .hljs-selector-id, .hljs-title {
    color: #61aeee;
}
.hljs-emphasis {
    font-style: italic;
}
.hljs-strong {
    font-weight: bold;
}
.hljs-link {
    text-decoration: underline;
}
/* doc-header css start */
/* checkout section css start */
.checkout-section {
    padding: 7.5rem 0;
}
.checkout-wrapper {
    position: relative;
    padding: 1.875rem 2.5rem;
    background-color: #fff;
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e5e5;
    z-index: 1;
}
@media (max-width: 480px) {
    .checkout-wrapper {
        padding: 1.875rem 1.25rem;
    }
}
.checkout-wrapper .shape {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0.05;
    z-index: -1;
}
.checkout-wrapper .shape-two {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    opacity: 0.05;
    z-index: -1;
}
.checkout-wrapper .form-logo {
    max-height: 50px;
}
.checkout-wrapper__header .title {
    font-size: 1rem;
}
.checkout-wrapper .product-price {
    font-size: 1.75rem;
}
.checkout-wrapper.checkout-wrapper--dark {
    background-color: #002046;
}
.checkout-wrapper.checkout-wrapper--dark .form--control {
    background-color: #002046;
    color: #fff;
}
.checkout-wrapper.checkout-wrapper--dark .form--control::-webkit-input-placeholder {
    color: #ffffff80;
}
.checkout-wrapper.checkout-wrapper--dark .form--control::-moz-placeholder {
    color: #ffffff80;
}
.checkout-wrapper.checkout-wrapper--dark .form--control:-ms-input-placeholder {
    color: #ffffff80;
}
.checkout-wrapper.checkout-wrapper--dark .form--control:-moz-placeholder {
    color: #ffffff80;
}
.checkout-wrapper.checkout-wrapper--dark .form--control:placeholder-shown {
    border-color: rgba(255, 255, 255, 0.45);
}
.checkout-wrapper.checkout-wrapper--dark .product-price, .checkout-wrapper.checkout-wrapper--dark .title {
    color: #fff;
}
.checkout-wrapper.checkout-wrapper--dark .or-divider::after {
    background-color: rgba(255, 255, 255, 0.25);
}
.checkout-wrapper.checkout-wrapper--dark .or-divider span {
    background-color: #002046;
    color: rgba(255, 255, 255, 0.8);
}
.checkout-wrapper.checkout-wrapper--dark p {
    color: #fff;
}
.or-divider {
    position: relative;
    text-align: center;
    z-index: 1;
}
.or-divider::after {
    position: absolute;
    content: '';
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #cacaca;
    z-index: -1;
}
.or-divider span {
    background-color: #fff;
    padding: 0 0.625rem;
}
.checkout-footer-menu {
    margin: -0.1875rem -0.3125rem;
}
.checkout-footer-menu li {
    margin: 0.1875rem 0.3125rem;
}
.checkout-footer-menu li a {
    font-size: 0.875rem;
}
/* checkout section css end*/
/* email verification section css start */
.email-verification-section {
    padding: 3.125rem 0;
    min-height: 100vh;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
.email-verification-section .wrapper {
    width: 100%;
    text-align: center;
}
.email-verification-section .verification-wrapper {
    position: relative;
    display: inline-block;
    width: 380px;
    background-color: #fff;
    padding: 2.5rem 1.875rem;
    border: 1px solid #cacaca;
    box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.05);
}
/* email verification section css end */
/* footer section css start */
.footer-section {
    background-color: #fff;
}
.footer-section__top {
    padding: 3.125rem 0;
}
@media (max-width: 1199px) {
    .footer-section__top {
        padding: 1.875rem 0;
    }
}
.footer-section__bottom {
    padding: 0.9375rem 0;
    border-top: 1px solid #e5e5e5;
    background-color: #002046;
}
.footer-section__bottom p {
    color: #fff;
}
.inline-menu-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
.inline-menu-list li {
    margin: 0.3125rem 0.625rem;
}
.inline-menu-list li a {
    color: #464646;
    font-size: 0.875rem;
}
.inline-menu-list li a:hover {
    color: #4582ff;
}



/* footer section css end */

.switch {
    display: inline-block;
    height: 34px;
    position: relative;
    width: 60px;
    margin-top: .5rem!important;
  }
  
  .switch input {
    display:none;
  }
  
  .slider {
    background-color: #ccc;
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: .4s;
  }
  
  .slider:before {
    background-color: #fff;
    bottom: 4px;
    content: "";
    height: 26px;
    left: 4px;
    position: absolute;
    transition: .4s;
    width: 26px;
  }
  
  input:checked + .slider {
    background-color: #2C71FF ;
  }
  
  input:checked + .slider:before {
    transform: translateX(26px);
  }
  
  .slider.round {
    border-radius: 34px;
  }
  
  .slider.round:before {
    border-radius: 50%;
  }

  .add-money-card {
    height: 100%;
    padding:  20px 30px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.07);
    border-top: 2px solid #4582ff;
}

.add-money-card .title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
}
.add-money-details-list li {
    padding: 8px 0;
    font-size: 14px;
    border-bottom: 1px dashed #b1b1b1;
}
.add-money-details-list li:first-child {
    padding-top: 0;
}
.add-money-details-list li:last-child {
    border-bottom: none;
}
.add-money-details-list li .value {
    font-weight: 700;
}
.add-money-details-list li,
.add-money-details-bottom {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
.add-money-details-bottom {
    padding-top: 10px;
    border-top: 1px dashed #b1b1b1;
    font-weight: 700;
    margin-top: 10px;
}

.add-money-card.style--two {
    display: flex;
    flex-wrap: wrap;
    flex-flow: column;
    justify-content: space-between;
}

.add-moeny-card-middle {
    width: 100%;
    padding: 20px;
    background-color: #4582ff1A !important;
    border-radius: 5px;
}

@media (max-width: 480px) {
    .add-money-card {
        padding: 20px 20px;
    }
}

.user-deposit-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}
.user-deposit-wrapper > div {
    width: 100%;
}
.expired-time-circle {
    margin-top: 15px;
    border-radius: 50%;
    width: 110px;
    height: 110px;
    border: 5px solid #7367f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    overflow: hidden;
    padding: 50px;
}
.expired-time-circle {
    position: relative;
    border: none !important;
}

.expired-time-circle::before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 8px solid #7367f0;
}

.expired-time-circle.danger-border .animation-circle {
    border-color: #f44336 !important;
}


@keyframes clipCircle {
    0% {
      clip-path: polygon(50% 50%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%);
      /* center, top-center*/
    }
    12.5% {
      clip-path: polygon(50% 50%, 50% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%);
      /* center, top-center, top-left*/
    }
    25% {
      clip-path: polygon(50% 50%, 50% 0%, 0% 0%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%);
      /* center, top-center, top-left, left-center*/
    }
    37.5% {
      clip-path: polygon(50% 50%, 50% 0%, 0% 0%, 0% 50%, 0% 100%, 0% 100%, 0% 100%, 0% 100%, 0% 100%, 0% 100%);
      /* center, top-center, top-left, left-center, bottom-left*/
    }
    50% {
      clip-path: polygon(50% 50%, 50% 0%, 0% 0%, 0% 50%, 0% 100%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 50% 100%);
      /* center, top-center, top-left, left-center, bottom-left, bottom-center*/
    }
    62.5% {
      clip-path: polygon(50% 50%, 50% 0%, 0% 0%, 0% 50%, 0% 100%, 50% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%);
      /* center, top-center, top-left, left-center, bottom-left, bottom-center, bottom-right*/
    }
    75% {
      clip-path: polygon(50% 50%, 50% 0%, 0% 0%, 0% 50%, 0% 100%, 50% 100%, 100% 100%, 100% 50%, 100% 50%, 100% 50%);
      /* center, top-center, top-left, left-center, bottom-left, bottom-center, bottom-right, right-center*/
    }
    87.5% {
      clip-path: polygon(50% 50%, 50% 0%, 0% 0%, 0% 50%, 0% 100%, 50% 100%, 100% 100%, 100% 50%, 100% 0%, 100% 0%);
      /* center, top-center, top-left, left-center, bottom-left, bottom-center, bottom-right, right-center top-right*/
    }
    100% {
      clip-path: polygon(50% 50%, 50% 0%, 0% 0%, 0% 50%, 0% 100%, 50% 100%, 100% 100%, 100% 50%, 100% 0%, 50% 0%);
      /* center, top-center, top-left, left-center, bottom-left, bottom-center, bottom-right, right-center top-right, top-center*/
    }
  }

.table>:not(:first-child) {
    border-top: none !important;
}

@media (max-width: 767px) {
    .table-responsive--sm tr td.not-found, .table-responsive--md tr td.not-found, .table-responsive--lg tr td.not-found {
        padding-left: 0 !important;
        text-align: center !important;
    }
}
@media (max-width: 991px) {
    .table-responsive--sm tr td.not-found, .table-responsive--md tr td.not-found, .table-responsive--lg tr td.not-found {
        padding-left: 0 !important;
        text-align: center !important;
    }
}

.card-badge {
    position: absolute;
    right: 15px;
    top: 15px;
    z-index: 1;
}
@media (max-width: 375px) {
    .card-badge {
        right: 10px;
        top: 10px;
    }
}
.card-badge::before {
    position: absolute;
    content: "";
    width: 6px;
    height: 6px;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 50%;
}
.success.card-badge::before {
    background-color: #28c76f;
}
.warning.card-badge::before {
    background-color: #ff9f43;
}
.info.card-badge::before {
    background-color: #1e9ff2;
}
.primary.card-badge::before {
    background-color: #7367f0;
}
.danger.card-badge::before {
    background-color: #ea5455;
}
.secondary.card-badge::before {
    background-color: #868e96;
}


@media (max-width: 1199px) {
    .header .main-menu li .sub-menu li a::before {
        display: none !important;
    }
}


@media (max-width: 424px) { 
    .table-responsive--sm tr td.not-found, .table-responsive--md tr td.not-found{
        text-align: center !important;
    }
    .fs-sm-18{
        font-size: 18px;
    }
    
}


.btn-close:focus{
    box-shadow: none !important;
    outline: none !important;
}

.fw--bold {
    font-weight: 600;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    transition: background-color 5000s ease-in-out 0s;
    -webkit-text-fill-color: #000 !important;
}

.action--buttons .form-group  {
    margin-bottom: 0px;
}
@media (max-width: 575px) {
    .action--buttons .form-group {
        width: 49%;
    }
    .action--buttons .btn {
        font-size: 12px;
        padding: 6px 7px !important;
        width: 100%;
    }
}

.form-control:focus {
    box-shadow: none;
}

.custom-select-search-box input{
    border: none !important;
}

.btn-check:focus+.btn, .btn:focus{
    box-shadow: none;
}

.box-shadow{
    background-color: white;
    border: 1px solid #dddddd5c;
    box-shadow: 1px 1px 9px #00000012;
}

.box-shadow .card-header{
    background-color: #fff;
}