/* reset css start */
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600&family=Maven+Pro:wght@400;500;600;700&display=swap");
html {
    scroll-behavior: smooth;
}
body {
    font-family: "Open Sans", sans-serif;
    color: #464646;
    font-size: 1rem;
    padding: 0;
    margin: 0;
    font-weight: 400;
    position: relative;
    line-height: 1.7;
    background-color: #fff;
    -webkit-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;
    overflow-x: hidden;
}
body.page-trns-active {
    position: relative;
}
body.page-trns-active::after {
    position: absolute;
    content: "\f110";
    font-family: 'Line Awesome Free';
    font-weight: 900;
    color: #fff;
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    font-size: 4.5rem;
    z-index: 999;
    animation: spinRoll 1s infinite linear;
}
body.page-trns-active::before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #002046;
    z-index: 99;
    opacity: 0.65;
}
@-webkit-keyframes spinRoll {
    from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-moz-keyframes spinRoll {
    from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-ms-keyframes spinRoll {
    from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes spinRoll {
    from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
section {
    background-color: #fff;
}
img {
    max-width: 100%;
    height: auto;
    user-select: none;
}
select {
    cursor: pointer;
}
ul, ol {
    padding: 0;
    margin: 0;
    list-style: none;
}
button {
    cursor: pointer;
}
*:focus {
    outline: none;
}
button {
    border: none;
}
button:focus {
    outline: none;
}
span {
    display: inline-block;
}
a:hover {
    color: #4582ff;
}
/* reset css end */
/* global css strat */
.text--primary {
    color: #7367f0 !important;
}
.text--secondary {
    color: #868e96 !important;
}
.text--success {
    color: #28c76f !important;
}
.text--danger {
    color: #ea5455 !important;
}
.text--warning {
    color: #ff9f43 !important;
}
.text--info {
    color: #1e9ff2 !important;
}
.text--dark {
    color: #10163A !important;
}
.text--muted {
    color: #cccccc !important;
}
.text--base {
    color: #4582ff !important;
}
.text--dark {
    color: #373e4a !important;
}
/* background color css start */
.bg--primary {
    background-color: #7367f0 !important;
}
.bg--secondary {
    background-color: #868e96 !important;
}
.bg--success {
    background-color: #28c76f !important;
}
.bg--danger {
    background-color: #ea5455 !important;
}
.bg--warning {
    background-color: #ff9f43 !important;
}
.bg--info {
    background-color: #1e9ff2 !important;
}
.bg--dark {
    background-color: #10163A !important;
}
.bg--light {
    background-color: #eef4ff !important;
}
.bg--base {
    background-color: #4582ff !important;
}
/* background color css end */
.mb-30 {
    margin-bottom: 30px;
}
.mb-none-30 {
    margin-bottom: -30px;
}
.pt-50 {
    padding-top: 50px;
}
.pb-50 {
    padding-bottom: 50px;
}
.pt-100 {
    padding-top: 100px;
}
.pb-100 {
    padding-bottom: 100px;
}
.pt-120 {
    padding-top: 120px;
}
.pb-120 {
    padding-bottom: 120px;
}
.pt-150 {
    padding-top: 150px;
}
.pb-150 {
    padding-bottom: 150px;
}
.bg_img {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}
.section-shape {
    -webkit-clip-path: polygon(0 0, 100% 0, 100% 84%, 82% 95%, 48% 100%, 0 100%);
    clip-path: polygon(0 0, 100% 0, 100% 84%, 82% 95%, 48% 100%, 0 100%);
}
@media (max-width: 1199px) {
    .section-shape {
        -webkit-clip-path: polygon(0 0, 100% 0, 100% 95%, 82% 98%, 48% 100%, 0 100%);
        clip-path: polygon(0 0, 100% 0, 100% 95%, 82% 98%, 48% 100%, 0 100%);
    }
}
@media (max-width: 575px) {
    .section-shape {
        -webkit-clip-path: polygon(0 0, 100% 0, 100% 98%, 100% 98%, 75% 100%, 0 100%);
        clip-path: polygon(0 0, 100% 0, 100% 98%, 100% 98%, 75% 100%, 0 100%);
    }
}
.section-shape-two {
    -webkit-clip-path: polygon(0 10%, 100% 5%, 100% 90%, 0% 100%);
    clip-path: polygon(0 10%, 100% 5%, 100% 90%, 0% 100%);
}
@media (max-width: 767px) {
    .section-shape-two {
        -webkit-clip-path: polygon(0 8%, 100% 5%, 100% 95%, 0% 100%);
        clip-path: polygon(0 8%, 100% 5%, 100% 95%, 0% 100%);
    }
}
@media (max-width: 575px) {
    .section-shape-two {
        -webkit-clip-path: polygon(0 3%, 100% 5%, 100% 98%, 0% 100%);
        clip-path: polygon(0 3%, 100% 5%, 100% 98%, 0% 100%);
    }
}
.section--bg {
    background-color: #F5F7FA;
}
.section--bg2 {
    background-color: #002046;
}
.section-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}
.dark-overlay {
    position: relative;
    z-index: 1;
}
.dark-overlay::before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #002046;
    opacity: 0.4;
    z-index: -1;
}
.opacity20 {
    opacity: 0.2;
}
.opacity30 {
    opacity: 0.3;
}
.opacity50 {
    opacity: 0.5;
}
.bg--one {
    background-color: #071e3e;
}
.slick-arrow {
    cursor: pointer;
}
.z-index-2 {
    z-index: 2;
}
.main-wrapper {
    position: relative;
    overflow: hidden;
}
.section-header {
    margin-bottom: 4.0625rem;
}
.section-title {
    font-size: 3rem;
}
@media (max-width: 1199px) {
    .section-title {
        font-size: 2.625rem;
    }
}
@media (max-width: 991px) {
    .section-title {
        font-size: 2.25rem;
    }
}
.section-subtitle {
    font-family: "Maven Pro", sans-serif;
    font-size: 1rem;
    font-weight: 500;
}
.section-subtitle.border-left {
    padding-left: 2.1875rem;
    position: relative;
    z-index: 1;
}
.section-subtitle.border-left::before {
    position: absolute;
    content: '';
    top: 50%;
    left: 0;
    width: 25px;
    height: 2px;
    background-color: #4582ff;
    margin-top: -1px;
}
a.text-white:hover {
    color: #4582ff !important;
}
.text--link {
    text-decoration: underline;
}
.text--link:hover {
    text-decoration: underline;
}
.has--link {
    position: relative;
}
.has--link .item--link {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}
.custom--dropdown .dropdown-toggle.no-arrow::after {
    display: none;
}
.custom--dropdown .dropdown-toggle::after {
    content: "\f107";
    border: none;
    font-family: 'Line Awesome Free';
    font-weight: 900;
    -webkit-transform: translateY(3px);
    -ms-transform: translateY(3px);
    transform: translateY(3px);
}
.custom--dropdown .dropdown-menu {
    border-color: #e5e5e5;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
}
.custom--dropdown .dropdown-menu li {
    border-bottom: 1px dashed #e5e5e5;
}
.custom--dropdown .dropdown-menu li:last-child {
    border-bottom: none;
}
.custom--dropdown .dropdown-menu li .dropdown-item {
    color: #464646;
    font-size: 0.875rem;
}
.custom--dropdown .dropdown-menu li .dropdown-item:hover {
    color: #4582ff;
    background-color: rgba(69, 130, 255, 0.05);
}
.custom--accordion .accordion-item + .accordion-item {
    margin-top: 1.25rem;
}
.custom--accordion .accordion-item {
    border: 1px solid rgba(69, 130, 255, 0.5);
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.custom--accordion .accordion-item:first-child .accordion-button {
    border-top: none;
}
.custom--accordion .accordion-item:last-child .accordion-button {
    border-bottom: none;
}
.custom--accordion .accordion-button {
    padding: 1.25rem 1.5625rem;
    background-color: rgba(69, 130, 255, 0.05);
    font-size: 1.125rem;
    position: relative;
    text-align: left;
}
.custom--accordion .accordion-button::after {
    position: absolute;
    top: 1.25rem;
    right: 0.8125rem;
    font-size: 1.0625rem;
    content: '\f107';
    font-family: 'Line Awesome Free';
    font-weight: 900;
    background-image: none;
    color: #000;
}
.custom--accordion .accordion-button:not(.collapsed) {
    background-color: #4582ff;
    color: #fff;
}
.custom--accordion .accordion-button:not(.collapsed)::after {
    color: #fff;
}
.custom--accordion .accordion-button:focus {
    box-shadow: none;
    outline: none;
    border-color: transparent;
}
.custom--accordion .accordion-body {
    padding: 1.25rem 1.5625rem;
}
.page-breadcrumb {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: 0.9375rem;
}
.page-breadcrumb li {
    color: rgba(255, 255, 255, 0.8);
    text-transform: capitalize;
}
.page-breadcrumb li::after {
    content: '-';
    color: #ffffff;
    margin: 0 0.3125rem;
}
.page-breadcrumb li:first-child::before {
    content: "\f015";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: #4582ff;
    margin-right: 0.375rem;
}
.page-breadcrumb li:last-child::after {
    display: none;
}
.page-breadcrumb li a {
    color: #ffffff;
    text-transform: capitalize;
}
.page-breadcrumb li a:hover {
    color: #4582ff;
}
.cmn-list li + li {
    margin-top: 0.9375rem;
}
.cmn-list li {
    position: relative;
    padding-left: 2.1875rem;
}
.cmn-list li::before {
    position: absolute;
    top: 0;
    left: 0;
    font-family: 'Line Awesome Free';
    font-weight: 900;
    content: "\f058";
    font-size: 1.75rem;
    color: #4582ff;
    margin-right: 0.5rem;
    line-height: 1;
}
.cmn-list-two li {
    padding: 0.375rem 0.9375rem;
    font-size: 0.875rem;
}
.cmn-list-two li:nth-child(even) {
    background-color: #EBF5F5;
}
.number-list {
    list-style: decimal;
    padding-left: 1.125rem;
}
.number-list li + li {
    margin-top: 0.625rem;
}
.disc-list li + li {
    margin-top: 0.625rem;
}
.disc-list li {
    position: relative;
    padding-left: 0.9375rem;
}
.disc-list li::before {
    position: absolute;
    content: '';
    top: 50%;
    left: 0;
    width: 0.375rem;
    height: 0.375rem;
    margin-top: -0.1875rem;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    background-color: #bdbdbd;
}
.caption-list li {
    display: flex;
    flex-wrap: wrap;
    padding: 0.625rem 0;
    font-size: 0.9375rem;
    border-bottom: 1px dashed #e5e5e5;
}
.caption-list li:first-child {
    padding-top: 0;
}
.caption-list li:last-child {
    padding-bottom: 0;
    border-bottom: none;
}
.caption-list li .caption {
    width: 30%;
    font-family: "Maven Pro", sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    position: relative;
}
@media (max-width: 480px) {
    .caption-list li .caption {
        width: 35%;
    }
}
.caption-list li .caption::after {
    position: absolute;
    content: ':';
    top: 0;
    right: 0;
}
.caption-list li .value {
    width: 70%;
    padding-left: 0.9375rem;
}
@media (max-width: 480px) {
    .caption-list li .value {
        width: 65%;
    }
}
.caption-list-two {
    padding: 0.625rem 0.9375rem;
    background-color: rgba(69, 130, 255, 0.1);
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.caption-list-two li {
    font-family: "Maven Pro", sans-serif;
    font-weight: 500;
    color: #373e4a;
    font-size: 0.875rem;
    padding: 0.5rem 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    border-bottom: 1px dashed #cccccc;
}
.caption-list-two li:first-child {
    padding-top: 0;
}
.caption-list-two li:last-child {
    padding-bottom: 0;
    border-bottom: none;
}
.caption-list-two li .caption {
    width: 20%;
    position: relative;
}
.caption-list-two li .caption::after {
    position: absolute;
    content: ':';
    top: 0;
    right: 0;
}
.caption-list-two li .value {
    width: 80%;
    text-align: right;
}
body, .btn--base.btn--custom, .custom--form-group .form--control ~ label, .header .main-menu li.menu_has_children > a::before, .header .main-menu li .sub-menu, .header .main-menu li .sub-menu li a, .header .main-menu li .sub-menu li a::before, .service-card, .choose-card, .user-account-check label, .user-account-check label i, .footer-contact-card .icon, .footer-contact-card .icon::after {
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.object-fit--cover {
    object-fit: cover;
    -o-object-fit: cover;
    object-position: center;
    -o-object-position: center;
}
.pagination {
    margin: -0.3125rem -0.4375rem;
    flex-wrap: wrap;
    justify-content: center;
}

.pagination .page-item {
    margin: 0.3125rem 0.4375rem;
}

.pagination .page-item.active .page-link {
    background-color: #4582ff;
    color: #fff;
}

.pagination .page-item .page-link {
    background-color: #e4e4e4;
    width: 2.8125rem;
    height: 2.8125rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #777;
}

.pagination .page-item .page-link:hover {
    background-color: #4582ff;
    border-color: #4582ff;
    color: #fff;
}

.pagination-md .page-item .page-link {
    width: 2.5rem;
    height: 2.5rem;
}

.pagination-sm .page-item .page-link {
    width: 2.1875rem;
    height: 2.1875rem;
    font-size: 0.875rem;
}
.video--btn {
    width: 7.5rem;
    height: 5.625rem;
    background-color: #4582ff;
    color: #fff;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    bottom: 0;
    right: 0;
    font-size: 2.625rem;
    border: 4px solid #fff;
}
.video--btn:hover {
    color: #fff;
}
.shake {
    animation: shake 0.5s 1 linear;
}
@-webkit-keyframes shake {
    0%, 100% {
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translateX(-10px);
        -ms-transform: translateX(-10px);
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        -webkit-transform: translateX(10px);
        -ms-transform: translateX(10px);
        transform: translateX(10px);
    }
}
@-moz-keyframes shake {
    0%, 100% {
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translateX(-10px);
        -ms-transform: translateX(-10px);
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        -webkit-transform: translateX(10px);
        -ms-transform: translateX(10px);
        transform: translateX(10px);
    }
}
@-ms-keyframes shake {
    0%, 100% {
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translateX(-10px);
        -ms-transform: translateX(-10px);
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        -webkit-transform: translateX(10px);
        -ms-transform: translateX(10px);
        transform: translateX(10px);
    }
}
@keyframes shake {
    0%, 100% {
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translateX(-10px);
        -ms-transform: translateX(-10px);
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        -webkit-transform: translateX(10px);
        -ms-transform: translateX(10px);
        transform: translateX(10px);
    }
}
.fadeInUp {
    -webkit-animation-name: fadeInUp;
    animation-name: fadeInUp;
}
@-webkit-keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
        -ms-transform: translateY(20px);
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}
@-moz-keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
        -ms-transform: translateY(20px);
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}
@-ms-keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
        -ms-transform: translateY(20px);
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}
@keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
        -ms-transform: translateY(20px);
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}
.fadeInLeft {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft;
}
@-webkit-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
        -ms-transform: translateX(-20px);
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@-moz-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
        -ms-transform: translateX(-20px);
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@-ms-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
        -ms-transform: translateX(-20px);
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
        -ms-transform: translateX(-20px);
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
.fadeInRight {
    -webkit-animation-name: fadeInRight;
    animation-name: fadeInRight;
}
@-webkit-keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
        -ms-transform: translateX(20px);
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@-moz-keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
        -ms-transform: translateX(20px);
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@-ms-keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
        -ms-transform: translateX(20px);
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
@keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
        -ms-transform: translateX(20px);
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }
}
.slideInLeft {
    -webkit-animation-name: slideInLeft;
    animation-name: slideInLeft;
}
@-webkit-keyframes slideInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-2000px);
        transform: translateX(-2000px);
    }
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}
@-moz-keyframes slideInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-2000px);
        transform: translateX(-2000px);
    }
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}
@-ms-keyframes slideInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-2000px);
        transform: translateX(-2000px);
    }
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}
@keyframes slideInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-2000px);
        transform: translateX(-2000px);
    }
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}
.slideInRight {
    -webkit-animation-name: slideInRight;
    animation-name: slideInRight;
}
@-webkit-keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
@-moz-keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
@-ms-keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
@keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
/* global css end */
/* preloader css start */
@-webkit-keyframes preloader-inside-white {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@-moz-keyframes preloader-inside-white {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@-ms-keyframes preloader-inside-white {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@keyframes preloader-inside-white {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@-webkit-keyframes preloader-inside-red {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    30% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@-moz-keyframes preloader-inside-red {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    30% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@-ms-keyframes preloader-inside-red {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    30% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

@keyframes preloader-inside-red {
    0% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    30% {
        -webkit-transform: scale(0, 0);
        -ms-transform: scale(0, 0);
        transform: scale(0, 0);
    }

    100% {
        -webkit-transform: scale(1, 1);
        -ms-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}

.preloader {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999999999;
    background: white;
    text-align: center;
}

.preloader .preloader-container {
    display: inline-block;
    width: 100px;
    height: 100px;
    position: relative;
}

.preloader .animated-preloader {
    display: inline-block;
    width: 100px;
    height: 100px;
    position: absolute;
    top: 0;
    left: 0;
    background: #0087FF;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.preloader .animated-preloader::after {
    content: '';
    display: inline-block;
    width: 100px;
    height: 100px;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    background: white;
    -webkit-animation: preloader-inside-white 1s ease-in-out infinite;
    -ms-animation: preloader-inside-white 1s ease-in-out infinite;
    animation: preloader-inside-white 1s ease-in-out infinite;
}

.preloader .animated-preloader::before {
    content: '';
    display: inline-block;
    width: 100px;
    height: 100px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    background: #0087FF;
    -webkit-animation: preloader-inside-red 1s ease-in-out infinite;
    -ms-animation: preloader-inside-red 1s ease-in-out infinite;
    animation: preloader-inside-red 1s ease-in-out infinite;
}


/* preloader css end */
h1 {
    font-size: 3.875rem;
}
h2 {
    font-size: 2rem;
}
@media (max-width: 991px) {
    h2 {
        font-size: 1.875rem;
    }
}
@media (max-width: 575px) {
    h2 {
        font-size: 1.75rem;
    }
}
h3 {
    font-size: 1.5rem;
}
h4 {
    font-size: 1.375rem;
}
@media (max-width: 767px) {
    h4 {
        font-size: 1.25rem;
    }
}
h5 {
    font-size: 1.25rem;
}
@media (max-width: 767px) {
    h5 {
        font-size: 1.125rem;
    }
}
h6 {
    font-size: 1.125rem;
}
h1, h2, h3, h4, h5, h6 {
    font-family: "Maven Pro", sans-serif;
    color: #373e4a;
    font-weight: 500;
    margin: 0;
    line-height: 1.2;
    word-break: break-word;
}
h1 > a, h2 > a, h3 > a, h4 > a, h5 > a, h6 > a {
    font-family: "Maven Pro", sans-serif;
    color: #373e4a;
    font-weight: 500;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    line-height: 1.2;
    word-break: break-word;
}
p, li, span {
    margin: 0;
}
a {
    text-decoration: none;
    display: inline-block;
    font-family: "Open Sans", sans-serif;
    font-weight: 400;
}
a:hover {
    text-decoration: none;
}
.font-size--18px {
    font-size: 1.125rem !important;
}
.font-size--16px {
    font-size: 1rem !important;
}
.font-size--14px {
    font-size: 0.875rem !important;
}
.font-size--12px {
    font-size: 0.75rem !important;
}
/* button css start */
button:focus {
    outline: none;
}
[class*="btn--"]:not(.btn--link):not(.btn--light) {
    color: #fff;
}
.btn {
    padding: 0.875rem 1.875rem;
}
.btn:focus {
    box-shadow: none;
}
.btn--primary {
    background-color: #7367f0;
}
.btn--primary:hover {
    background-color: #5e50ee;
}
.btn--secondary {
    background-color: #868e96;
}
.btn--secondary:hover {
    background-color: #78818a;
}
.btn--success {
    background-color: #28c76f;
}
.btn--success:hover {
    background-color: #24b263;
}
.btn--danger {
    background-color: #ea5455;
}
.btn--danger:hover {
    background-color: #e73d3e;
}
.btn--warning {
    background-color: #ff9f43;
}
.btn--warning:hover {
    background-color: #ff922a;
}
.btn--info {
    background-color: #1e9ff2;
}
.btn--info:hover {
    background-color: #0d93e9;
}
.btn--light {
    background-color: #eef4ff;
}
.btn--light:hover {
    background-color: #d5e4ff;
}
.btn--dark {
    background-color: #10163A;
    color: #fff;
}
.btn--dark:hover {
    background-color: #0a0e26;
    color: #fff;
}
.btn--link {
    color: #7367f0;
}
.btn--base {
    background-color: #4582ff;
    color: #fff;
}
.btn--base:hover {
    background-color: #2c71ff;
    color: #fff;
}
.btn--base.btn--custom {
    outline: 2px solid rgba(255, 255, 255, 0.7);
    outline-offset: -6px;
}
.btn--base.btn--custom:hover {
    outline-offset: 0;
    outline: 2px solid rgba(255, 255, 255, 0);
}
.text-btn {
    padding: 0;
    color: #464646;
    background-color: transparent;
}
.arrow-btn {
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    color: #464646;
    display: flex;
    flex-wrap: wrap;
    min-height: 4.375rem;
}
.arrow-btn i {
    width: 95px;
    font-size: calc((5.9375rem / 2) - 0.75rem);
    -webkit-clip-path: polygon(75% 0%, 100% 50%, 75% 100%, 0% 100%, 20% 50%, 0% 0%);
    clip-path: polygon(75% 0%, 100% 50%, 75% 100%, 0% 100%, 20% 50%, 0% 0%);
    background-color: #4582ff;
    color: #fff;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
}
.arrow-btn span {
    width: calc(100% - 95px);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-family: "Maven Pro", sans-serif;
    background-color: #fff;
    z-index: 1;
    position: relative;
    padding: 0.625rem;
}
.arrow-btn span::before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: calc(100% + 50px);
    height: 100%;
    background-color: #fff;
    box-shadow: 0 0 3px #2f2f2f1f;
    border: 1px solid #d6d6d6;
    left: -50px;
    z-index: -1;
    border-radius: 0 5px 5px 0;
}
.btn-outline--primary {
    color: #7367f0;
    border-color: #7367f0;
}
.btn-outline--primary:hover {
    background-color: #7367f0;
    color: #ffffff;
}
.btn-outline--secondary {
    color: #868e96;
    border-color: #868e96;
}
.btn-outline--secondary:hover {
    background-color: #868e96;
    color: #ffffff;
}
.btn-outline--success {
    color: #28c76f;
    border-color: #28c76f;
}
.btn-outline--success:hover {
    background-color: #28c76f;
    color: #ffffff;
}
.btn-outline--danger {
    color: #ea5455;
    border-color: #ea5455;
}
.btn-outline--danger:hover {
    background-color: #ea5455;
    color: #ffffff;
}
.btn-outline--warning {
    color: #ff9f43;
    border-color: #ff9f43;
}
.btn-outline--warning:hover {
    background-color: #ff9f43;
    color: #ffffff;
}
.btn-outline--info {
    color: #1e9ff2;
    border-color: #1e9ff2;
}
.btn-outline--info:hover {
    background-color: #1e9ff2;
    color: #ffffff;
}
.btn-outline--light {
    color: #eef4ff;
    border-color: #eef4ff;
}
.btn-outline--light:hover {
    background-color: #eef4ff;
    color: #ffffff;
}
.btn-outline--dark {
    color: #10163A;
    border-color: #10163A;
}
.btn-outline--dark:hover {
    background-color: #10163A;
    color: #ffffff;
}
.btn-outline--base {
    color: #4582ff;
    border: 1px solid #4582ff;
}
.btn-outline--base:hover {
    background-color: #4582ff;
    color: #fff;
}
.btn-shadow--primary {
    box-shadow: 0 0 6px 1px rgba(115, 103, 240, 0.35);
}
.btn-shadow--secondary {
    box-shadow: 0 0 6px 1px rgba(134, 142, 150, 0.35);
}
.btn-shadow--success {
    box-shadow: 0 0 6px 1px rgba(40, 199, 111, 0.35);
}
.btn-shadow--danger {
    box-shadow: 0 0 6px 1px rgba(234, 84, 85, 0.35);
}
.btn-shadow--warning {
    box-shadow: 0 0 6px 1px rgba(255, 159, 67, 0.35);
}
.btn-shadow--info {
    box-shadow: 0 0 6px 1px rgba(30, 159, 242, 0.35);
}
.btn-shadow--light {
    box-shadow: 0 0 6px 1px rgba(238, 244, 255, 0.35);
}
.btn-shadow--dark {
    box-shadow: 0 0 6px 1px rgba(16, 22, 58, 0.35);
}
.btn-shadow--base {
    box-shadow: 0 0 6px 1px rgba(69, 130, 255, 0.35);
}
.btn--capsule {
    border-radius: 999px;
    -webkit-border-radius: 999px;
    -moz-border-radius: 999px;
    -ms-border-radius: 999px;
    -o-border-radius: 999px;
}
.icon-btn {
    width: 1.5625rem;
    height: 1.5625rem;
    background-color: #4582ff;
    color: #fff;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}
.icon-btn:hover {
    color: #fff;
}
.video-btn {
    display: inline-flex;
    align-items: center;
}
.video-btn .icon {
    width: 3.75rem;
    height: 3.75rem;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background-color: #4582ff;
    color: #fff;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    font-size: calc(3.75rem / 2);
    position: relative;
    z-index: 1;
}
.video-btn .icon::before, .video-btn .icon::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: 74px;
    background-color: #4582ff;
    opacity: 0.15;
    z-index: -1;
}
.video-btn .icon::after {
    -webkit-animation: outer-ripple 2000ms linear infinite;
    -moz-animation: outer-ripple 2000ms linear infinite;
    animation: outer-ripple 2000ms linear infinite;
}
.video-btn .icon::before {
    -webkit-animation: inner-ripple 2000ms linear infinite;
    -moz-animation: inner-ripple 2000ms linear infinite;
    animation: inner-ripple 2000ms linear infinite;
}
.video-btn span {
    margin-left: 10px;
}
.video-btn.video-btn--lg .icon {
    width: 5.625rem;
    height: 5.625rem;
    font-size: calc(5.625rem / 2);
}
@-webkit-keyframes outer-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        -webkit-filter: alpha(opacity=50);
    }
    80% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
    100% {
        transform: scale(2.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(2.5);
        -moz-transform: scale(2.5);
        -ms-transform: scale(2.5);
        -o-transform: scale(2.5);
    }
}
@-moz-keyframes outer-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        -webkit-filter: alpha(opacity=50);
    }
    80% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
    100% {
        transform: scale(2.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(2.5);
        -moz-transform: scale(2.5);
        -ms-transform: scale(2.5);
        -o-transform: scale(2.5);
    }
}
@-ms-keyframes outer-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        -webkit-filter: alpha(opacity=50);
    }
    80% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
    100% {
        transform: scale(2.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(2.5);
        -moz-transform: scale(2.5);
        -ms-transform: scale(2.5);
        -o-transform: scale(2.5);
    }
}
@keyframes outer-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        -webkit-filter: alpha(opacity=50);
    }
    80% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
    100% {
        transform: scale(2.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(2.5);
        -moz-transform: scale(2.5);
        -ms-transform: scale(2.5);
        -o-transform: scale(2.5);
    }
}
@-webkit-keyframes inner-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    30% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    100% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
}
@-moz-keyframes inner-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    30% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    100% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
}
@-ms-keyframes inner-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    30% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    100% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
}
@keyframes inner-ripple {
    0% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    30% {
        transform: scale(1);
        filter: alpha(opacity=50);
        opacity: 0.5;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
    100% {
        transform: scale(1.5);
        filter: alpha(opacity=0);
        opacity: 0;
        -webkit-transform: scale(1.5);
        -moz-transform: scale(1.5);
        -ms-transform: scale(1.5);
        -o-transform: scale(1.5);
    }
}
.btn--group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
    margin-left: -0.625rem;
    margin-right: -0.625rem;
}
.btn--group *[class*="btn"] {
    margin: 0.3125rem 0.625rem;
    align-items: center;
}
.btn--group *[class*="btn"].d-flex {
    padding: 0.5rem 2.1875rem;
}
.btn--group.style--two {
    margin-left: -0.3125rem;
    margin-right: -0.3125rem;
}
.btn--group.style--two *[class*="btn"] {
    margin: 0.1875rem 0.3125rem;
}
[class*="btn"].btn-md {
    padding: 0.625rem 1.25rem;
}
[class*="btn"].btn-sm {
    padding: 0.375rem 0.625rem;
}
/* button css end */
.badge--primary {
    background-color: rgba(115, 103, 240, 0.15);
    border: 1px solid #7367f0;
    color: #7367f0;
}
.badge--secondary {
    background-color: rgba(134, 142, 150, 0.15);
    border: 1px solid #868e96;
    color: #868e96;
}
.badge--success {
    background-color: rgba(40, 199, 111, 0.15);
    border: 1px solid #28c76f;
    color: #28c76f;
}
.badge--danger {
    background-color: rgba(234, 84, 85, 0.15);
    border: 1px solid #ea5455;
    color: #ea5455;
}
.badge--warning {
    background-color: rgba(255, 159, 67, 0.15);
    border: 1px solid #ff9f43;
    color: #ff9f43;
}
.badge--info {
    background-color: rgba(30, 159, 242, 0.15);
    border: 1px solid #1e9ff2;
    color: #1e9ff2;
}
.badge--light {
    background-color: rgba(238, 244, 255, 0.15);
    border: 1px solid #eef4ff;
    color: #eef4ff;
}
.badge--dark {
    background-color: rgba(16, 22, 58, 0.15);
    border: 1px solid #10163A;
    color: #10163A;
}
.badge--base {
    background-color: rgba(69, 130, 255, 0.15);
    border: 1px solid #4582ff;
    color: #4582ff;
}
/* table css start */
.custom--table {
    background-color: #fff;
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
}
.custom--table.white-space-nowrap th {
    white-space: nowrap;
}
.custom--table thead tr {
    box-shadow: 0 5px 10px 2px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
}
.custom--table thead th {
    border-top: none;
    padding: 0.625rem 1.25rem;
    color: #fff;
    background-color: #002046;
    border: none;
    font-size: 0.8125rem;
    text-transform: uppercase;
    font-weight: 700;
}
.custom--table thead th:first-child {
    border-radius: 5px 0 0 5px;
    -webkit-border-radius: 5px 0 0 5px;
    -moz-border-radius: 5px 0 0 5px;
    -ms-border-radius: 5px 0 0 5px;
    -o-border-radius: 5px 0 0 5px;
}
.custom--table thead th:last-child {
    border-radius: 0 5px 5px 0;
    -webkit-border-radius: 0 5px 5px 0;
    -moz-border-radius: 0 5px 5px 0;
    -ms-border-radius: 0 5px 5px 0;
    -o-border-radius: 0 5px 5px 0;
}
.custom--table tbody td {
    border-top: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07);
    padding: 0.9375rem 1.25rem;
    color: #464646;
    vertical-align: middle;
    font-size: 0.875rem;
}
.custom--table tbody tr {
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.custom--table tbody tr:nth-child(even) {
    background-color: #afb1b50d;
}
.custom--table tbody tr:last-child td {
    border-bottom: none;
}
[data-label] {
    position: relative;
}
[data-label]::before {
    position: absolute;
    content: attr(data-label);
    font-weight: 700;
    color: #000000;
    top: 0;
    left: 0;
    padding: 0.8125rem 0.9375rem;
    display: none;
    font-size: 0.75rem;
}
@media (max-width: 991px) {
    .table-responsive--md thead {
        display: none;
    }
    .table-responsive--md tbody tr:nth-child(odd) {
        background-color: aliceblue;
    }
    .table-responsive--md tr th, .table-responsive--md tr td {
        display: block;
        padding-left: 45% !important;
        text-align: right !important;
        border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
    }
    .table-responsive--md tr th:first-child, .table-responsive--md tr td:first-child {
        border-top: none !important;
    }
    .table-responsive--md [data-label]::before {
        display: block;
    }
}
@media (max-width: 767px) {
    .table-responsive--sm thead {
        display: none;
    }
    .table-responsive--sm tbody tr:nth-child(odd) {
        background-color: aliceblue;
    }
    .table-responsive--sm tr th, .table-responsive--sm tr td {
        display: block;
        padding-left: 45% !important;
        text-align: right !important;
        border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
    }
    .table-responsive--sm tr th:first-child, .table-responsive--sm tr td:first-child {
        border-top: none !important;
    }
    .table-responsive--sm [data-label]::before {
        display: block;
    }
}
@media (max-width: 1199px) {
    *[class*="table-responsive--"].data-label--none tr th, *[class*="table-responsive--"].data-label--none tr td {
        padding-left: .75rem;
    }
}
/* table css end */
/* form css start */
.form-group {
    margin-bottom: 1.25rem;
}
.form--control {
    padding: 0.625rem 1.25rem;
    border: 1px solid #e3e3e3 !important;
    width: 100%;
    background-color: #fff;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    color: #000;
    height: 3.125rem;
}
.form--control:focus{
    border-color: #bfbcbc !important;
    transition: all .3s;
}
.form--control[readonly], .form--control[disabled]{
    background-color: #f5f5f5;
    cursor: no-drop;
}
.form--control.style--two {
    border-width: 0 0 1px 0;
    padding: 0.625rem 0;
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
    font-family: "Maven Pro", sans-serif;
    border-bottom-color: #999999;
}
.form--control.style--two:focus {
    box-shadow: none;
}

.custom--form-group {
    position: relative;
}
.custom--form-group .form--control {
    border-color: #e5e5e5;
    border-width: 0 0 1px 0;
    padding: 0.625rem 0;
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
    position: relative;
    z-index: 2;
    background-color: transparent;
}
.custom--form-group .form--control:focus, .custom--form-group .form--control.hascontent {
    box-shadow: none;
}
.custom--form-group .form--control:focus ~ label, .custom--form-group .form--control.hascontent ~ label {
    top: -5px;
    font-size: 0.75rem;
}
.custom--form-group .form--control ~ label {
    color: #535353;
    font-size: 0.8125rem;
    font-weight: 400;
    position: absolute;
    top: 15px;
    left: 0;
    z-index: 1;
}

.select {
    padding: 0.625rem 1.25rem;
    width: 100%;
    border: 1px solid #e5e5e5;
    cursor: pointer;
    color: #464646;
    background-color: #fff;
    height: 3.125rem;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
}
.select option {
    padding: 0.625rem 0;
    display: block;
    border-top: 1px solid #e5e5e5;
}
.select.style--trans {
    background-color: transparent;
    color: #fff;
    border-color: rgba(255, 255, 255, 0.5);
}
.select.style--trans option {
    color: #363636;
}
.select.select-sm {
    height: 1.875rem;
    font-size: 0.875rem;
    padding: 0.3125rem;
}
textarea {
    min-height: 9.375rem !important;
    resize: none;
    width: 100%;
}
label {
    color: #535353;
    margin-bottom: 0.625rem;
    font-family: "Maven Pro", sans-serif;
    font-size: 0.8125rem;
    font-weight: 500;
}
.input-group > .form--control,
.input-group > .select {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}
.custom-radio {
    position: relative;
}
.custom-radio input[type=radio] {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    visibility: hidden;
    cursor: pointer;
}
.custom-radio input[type=radio]:checked ~ label::before {
    border-width: 2px;
    border-color: #4582ff;
}
.custom-radio input[type=radio]:checked ~ label::after {
    opacity: 1;
}
.custom-radio label {
    margin-bottom: 0;
    position: relative;
    padding-left: 20px;
}
.custom-radio label::before {
    position: absolute;
    content: '';
    top: 3px;
    left: 0;
    width: 15px;
    height: 15px;
    border: 1px solid #888888;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.custom-radio label::after {
    position: absolute;
    content: '';
    top: 7px;
    left: 4px;
    width: 7px;
    height: 7px;
    background-color: #4582ff;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    opacity: 0;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.wrong-info .form--control {
    border-color: #ea5455 !important;
    box-shadow: 0 0 6px 1px rgba(234, 84, 85, 0.3) !important;
}
/* form css end*/
/* card css start */
.custom--card {
    background-color: #fff;
    box-shadow: none;
    border: 1px solid #d6d6d6;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.custom--card .card-header {
    background-color: #4582ff08;
}
.custom--card .card-body {
    padding: 1.25rem;
}
.card {
    border-width: 2px;
}
.card.style--two {
    background-color: transparent;
}
.card.style--two .card-header {
    padding: 0.9375rem 1.5625rem;
    background-color: transparent;
}
/* card css end */
/* modal css start */
.modal {
    z-index: 999999;
}
/* modal css end */
/* header start */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9;
    background-color: transparent;
}
.header.menu-fixed .header__top {
    display: none;
}
.header.menu-fixed .header__bottom {
    background-color: #002046;
}
.header__top {
    padding: 0.625rem 3.125rem;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}
@media (max-width: 480px) {
    .header__top {
        padding: 0.5rem 0.9375rem;
    }
}
.header__bottom {
    padding: 0 3.125rem;
    background-color: rgba(0, 32, 70, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: none;
}
@media (max-width: 1199px) {
    .header__bottom {
        padding: 0.625rem 0;
    }
}
.header .site-logo img {
    width: 13.9375rem;
}
@media (max-width: 1199px) {
    .header .site-logo img {
        max-width: 9.375rem;
    }
}
.header .main-menu {
    margin-left: 4.375rem;
}
@media (max-width: 1199px) {
    .header .main-menu {
        margin-left: 0;
        padding: 0.9375rem 0;
    }
}
.header .main-menu li {
    position: relative;
}
@media (max-width: 1199px) {
    .header .main-menu li {
        border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    }
}
.header .main-menu li:last-child a {
    padding-right: 0;
}
.header .main-menu li.menu_has_children {
    position: relative;
}
.header .main-menu li.menu_has_children.open .sub-menu {
    display: block;
}
.header .main-menu li.menu_has_children > a {
    padding-right: 1.5625rem;
}
@media (max-width: 1199px) {
    .header .main-menu li.menu_has_children > a {
        display: block;
    }
}
.header .main-menu li.menu_has_children > a::before {
    position: absolute;
    content: "\f067";
    font-family: "Line Awesome Free";
    font-weight: 900;
    top: 30px;
    right: 0;
    color: #ffffff;
}
@media (max-width: 1199px) {
    .header .main-menu li.menu_has_children > a::before {
        display: block;
        top: 0.5625rem;
    }
}
.header .main-menu li.menu_has_children:hover > a::before {
    content: "\f068";
    color: #4582ff;
}
.header .main-menu li a {
    font-family: "Maven Pro", sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    padding: 1.875rem 0.9375rem 1.875rem 0;
    font-size: 1rem;
    color: #fff;
}
@media (max-width: 1199px) {
    .header .main-menu li a {
        color: #ffffff;
        padding: 0.5rem 0;
        display: block;
    }
}
.header .main-menu li a:hover, .header .main-menu li a:focus {
    color: #4582ff;
}
.header .main-menu li .sub-menu {
    position: absolute;
    width: 220px;
    top: 105%;
    left: 0;
    z-index: 9999;
    background-color: #fff;
    padding: 0.625rem 0;
    -webkit-box-shadow: 0px 18px 54px -8px rgba(0, 0, 0, 0.15);
    box-shadow: 0px 5px 25px 2px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    opacity: 0;
    visibility: hidden;
    border: 2px solid #e5e5e5;
}
@media (max-width: 1199px) {
    .header .main-menu li .sub-menu {
        opacity: 1;
        visibility: visible;
        display: none;
        position: static;
        -webkit-transition: none;
        -o-transition: none;
        transition: none;
        width: 100%;
        background-color: #002046;
        border: none;
    }
}
.header .main-menu li .sub-menu::before {
    position: absolute;
    content: '';
    top: -19px;
    left: 20px;
    border-width: 10px 10px 10px 10px;
    border-style: solid;
    border-color: transparent transparent #fff transparent;
}
@media (max-width: 1199px) {
    .header .main-menu li .sub-menu::before {
        display: none;
    }
}
.header .main-menu li .sub-menu li {
    border-bottom: 1px dashed #e5e5e5;
}
@media (max-width: 1199px) {
    .header .main-menu li .sub-menu li {
        border-color: rgba(255, 255, 255, 0.15);
    }
}
.header .main-menu li .sub-menu li:last-child {
    border-bottom: none;
}
.header .main-menu li .sub-menu li a {
    padding: 0.5rem 1.5625rem;
    display: block;
    color: #464646;
    position: relative;
    font-size: 0.9375rem;
    text-transform: capitalize;
}
@media (max-width: 1199px) {
    .header .main-menu li .sub-menu li a {
        color: #fff;
    }
}
.header .main-menu li .sub-menu li a::before {
    position: absolute;
    content: '';
    top: 0;
    left: -0.25rem;
    width: 0.25rem;
    height: 100%;
    background-color: #4582ff;
    opacity: 0;
}
.header .main-menu li .sub-menu li a:hover {
    background-color: rgba(69, 130, 255, 0.05);
    color: #4582ff;
}
.header .main-menu li .sub-menu li a:hover::before {
    opacity: 1;
    left: 0;
}
.header .main-menu li .sub-menu li + li {
    margin-left: 0;
}
.header .main-menu li:hover .sub-menu {
    top: 100%;
    opacity: 1;
    visibility: visible;
}
.header .main-menu li + li {
    margin-left: 1.25rem;
}
@media (max-width: 1399px) {
    .header .main-menu li + li {
        margin-left: .25rem;
    }
}
@media (max-width: 1199px) {
    .header .main-menu li + li {
        margin-left: 0;
    }
}
.header .nav-right {
    padding-left: 3.125rem;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
}
@media (max-width: 1199px) {
    .header .nav-right {
        padding-left: 0;
    }
}
.header-info-list {
    margin: -0.1875rem -0.625rem;
}
.header-info-list li {
    padding: 0.1875rem 0.625rem;
}
.header-info-list li a {
    font-size: 0.875rem;
    color: #fff;
}
.header-info-list li a i {
    font-size: 18px;
}
.header-info-list li a:hover {
    color: #4582ff;
}
.social-links {
    margin: -0.1875rem -0.3125rem;
}
.social-links li {
    margin: 0.1875rem 0.3125rem;
}
.social-links.style--white li a {
    color: #fff;
}
@media (max-width: 1199px) {
    .navbar-collapse {
        background-color: #071e3e;
        padding: 0 1.875rem 1.25rem 1.875rem;
    }
}
@media (max-width: 767px) {
    .navbar-collapse {
        max-height: 20rem;
        overflow: auto;
    }
}

.navbar-toggler {
    padding: 0;
    border: none;
}
.navbar-toggler:focus {
    outline: none;
    box-shadow: none;
}
.menu-toggle {
    margin: 10px 0;
    position: relative;
    display: block;
    width: 2.1875rem;
    height: 1.25rem;
    cursor: pointer;
    background: transparent;
    border-top: 2px solid;
    border-bottom: 2px solid;
    color: #ffffff;
    font-size: 0;
    -webkit-transition: all 0.25s ease-in-out;
    -o-transition: all 0.25s ease-in-out;
    transition: all 0.25s ease-in-out;
    cursor: pointer;
}
.menu-toggle:before, .menu-toggle:after {
    content: '';
    display: block;
    width: 100%;
    height: 2px;
    position: absolute;
    top: 50%;
    left: 50%;
    background: currentColor;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    transition: -webkit-transform 0.25s ease-in-out;
    -webkit-transition: -webkit-transform 0.25s ease-in-out;
    -o-transition: -webkit-transform 0.25s ease-in-out;
    transition: transform 0.25s ease-in-out;
    -moz-transition: -webkit-transform 0.25s ease-in-out;
    -ms-transition: -webkit-transform 0.25s ease-in-out;
}
@media (max-width: 1199px) {
    .menu-toggle:before, .menu-toggle:after {
        background-color: #ffffff;
    }
}
.navbar-toggler[aria-expanded="true"] span {
    border-color: transparent;
}
.navbar-toggler[aria-expanded="true"] span:before {
    -webkit-transform: translate(-50%, -50%) rotate(45deg);
    -ms-transform: translate(-50%, -50%) rotate(45deg);
    transform: translate(-50%, -50%) rotate(45deg);
}
.navbar-toggler[aria-expanded="true"] span:after {
    -webkit-transform: translate(-50%, -50%) rotate(-45deg);
    -ms-transform: translate(-50%, -50%) rotate(-45deg);
    transform: translate(-50%, -50%) rotate(-45deg);
}
.navbar-toggler[aria-expanded="true"] span.menu-toggle:hover {
    color: #ffffff;
}
@media (max-width: 1199px) {
    .navbar-toggler[aria-expanded="true"] span.menu-toggle:hover {
        color: #ffffff;
    }
}
.navbar-toggler[aria-expanded="true"] span {
    border-color: transparent;
}
.navbar-toggler[aria-expanded="true"] span:before {
    -webkit-transform: translate(-50%, -50%) rotate(45deg);
    -ms-transform: translate(-50%, -50%) rotate(45deg);
    transform: translate(-50%, -50%) rotate(45deg);
}
.navbar-toggler[aria-expanded="true"] span:after {
    -webkit-transform: translate(-50%, -50%) rotate(-45deg);
    -ms-transform: translate(-50%, -50%) rotate(-45deg);
    transform: translate(-50%, -50%) rotate(-45deg);
}
.animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}
.fadeInDown {
    -webkit-animation-name: fadeInDown;
    animation-name: fadeInDown;
}
@-webkit-keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-20px);
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@-moz-keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-20px);
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@-ms-keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-20px);
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-20px);
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
/* header end */
/* hero section css start */
.hero {
    padding-top: 18.75rem;
    padding-bottom: 15.625rem;
    position: relative;
    z-index: 1;
    overflow: hidden;
}
@media (max-width: 1199px) {
    .hero {
        padding-top: 14.375rem;
        padding-bottom: 8.75rem;
    }
}
@media (max-width: 420px) {
    .hero {
        padding-top: 18.75rem;
    }
}
.hero::after {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #002046;
    background: -webkit-linear-gradient(to right, rgba(0, 32, 70, 0.65), rgba(0, 32, 70, 0.01));
    background: linear-gradient(to right, rgba(0, 32, 70, 0.65), rgba(0, 32, 70, 0.01));
    z-index: -1;
}
.hero__subtitle {
    color: #fff;
    position: relative;
    padding: 0 2.1875rem;
}
.hero__subtitle::before {
    position: absolute;
    content: '';
    top: 50%;
    left: 0;
    width: 25px;
    height: 1px;
    background-color: #4582ff;
}
.hero__subtitle::after {
    position: absolute;
    content: '';
    top: 50%;
    right: 0;
    width: 25px;
    height: 1px;
    background-color: #4582ff;
}
.hero__title {
    font-size: 4.5rem;
    color: #fff;
    font-weight: 400;
}
@media (max-width: 1399px) {
    .hero__title {
        font-size: 4.125rem;
    }
}
@media (max-width: 1199px) {
    .hero__title {
        font-size: 3.375rem;
    }
}
@media (max-width: 575px) {
    .hero__title {
        font-size: 3rem;
    }
}
@media (max-width: 480px) {
    .hero__title {
        font-size: 2.25rem;
    }
}
.hero__des {
    font-size: 1.125rem;
    color: #fff;
}
@media (max-width: 1199px) {
    .hero__des {
        font-size: 1rem;
    }
}
/* hero section css end */
/* inner-hero css start */
.inner-hero {
    padding-top: 13.75rem;
    padding-bottom: 5rem;
    position: relative;
    z-index: 1;
}
@media (max-width: 1199px) {
    .inner-hero {
        padding-top: 11.875rem;
    }
}
@media (max-width: 400px) {
    .inner-hero {
        padding-top: 15.625rem;
    }
}
.inner-hero::before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #002046;
    opacity: 0.7;
    z-index: -1;
}
.page-title {
    font-size: 2.625rem;
}
@media (max-width: 1199px) {
    .page-title {
        font-size: 2.25rem;
    }
}
@media (max-width: 767px) {
    .page-title {
        font-size: 2rem;
    }
}
/* inner-hero css end */
/* brand-section css start */
.brand-section {
    padding: 2.1875rem 0;
    border-bottom: 1px solid #e5e5e5;
    background-color: #4582ff05;
}
@media(max-width: 991px){
    .brand-slider{
        margin-top: 15px;
    }
}
.brand-slider .slick-track {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
}
.brand-item img {
    display: inline-block;
}
/* brand-section css end */
/* about section css start */
.about-thumb {
    float: left;
    width: 140%;
    position: relative;
    margin-bottom: 1.875rem;
}
@media (max-width: 1750px) {
    .about-thumb {
        width: 130%;
    }
}
@media (max-width: 1650px) {
    .about-thumb {
        width: 120%;
    }
}
@media (max-width: 1550px) {
    .about-thumb {
        width: 110%;
    }
}
@media (max-width: 1450px) {
    .about-thumb {
        width: 100%;
    }
}
.about-thumb img {
    width: 100%;
}
.about-thumb .about-img-content {
    padding: 1.25rem 3.125rem;
    background-color: #4582ff;
    color: #fff;
    display: inline-block;
    text-align: center;
    position: absolute;
    bottom: -1.875rem;
    left: -5rem;
    z-index: 1;
}
@media (max-width: 1450px) {
    .about-thumb .about-img-content {
        padding: 1.25rem 1.875rem;
    }
}
@media (max-width: 1199px) {
    .about-thumb .about-img-content {
        left: -0.9375rem;
    }
}
@media (max-width: 575px) {
    .about-thumb .about-img-content {
        left: 0;
        padding: 0.625rem 0.9375rem;
    }
}
.about-thumb .about-img-content::after {
    position: absolute;
    content: '';
    background: transparent;
    background-image: radial-gradient(#fff 20%, transparent 0), radial-gradient(#fff 20%, transparent 0);
    background-size: 10px 10px;
    background-position: 0 0, 25px 25px;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.15;
    z-index: -1;
}
.about-thumb .about-img-content .years {
    font-size: 4.5rem;
    display: block;
    color: #fff;
}
@media (max-width: 1450px) {
    .about-thumb .about-img-content .years {
        font-size: 3.875rem;
    }
}
@media (max-width: 575px) {
    .about-thumb .about-img-content .years {
        font-size: 3rem;
    }
}
.about-thumb .about-img-content .caption {
    font-size: 1.375rem;
}
@media (max-width: 1450px) {
    .about-thumb .about-img-content .caption {
        font-size: 1.125rem;
    }
}
.award-list {
    margin: -0.4375rem -0.625rem;
}
.award-list li {
    margin: 0.4375rem 0.625rem;
}
.award-list li img {
    max-width: 6.25rem;
}
@media (max-width: 480px) {
    .award-list li img {
        max-width: 4.375rem;
    }
}
/* about section css end  */
/* service section css start */
.section-wave-img {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: -1;
}
.service-card {
    padding: 50px 30px;
    background-color: #002550;
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
    overflow: hidden;
    position: relative;
    border: 2px solid rgba(69, 130, 255, 0.45);
    position: relative;
    z-index: 1;
}
@media (max-width: 1199px) {
    .service-card {
        padding: 2.1875rem 1.25rem;
    }
}
.service-card:hover {
    border-color: #4582ff;
    box-shadow: 0 5px 15px 1px rgba(69, 130, 255, 0.25);
    -webkit-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    transform: translateY(-5px);
}
.service-card::before {
    position: absolute;
    content: '';
    bottom: 0;
    left: 0;
    width: 100%;
    height: 80%;
    clip-path: polygon(46% 66%, 76% 64%, 100% 41%, 100% 100%, 0 100%, 0 79%, 26% 79%);
    background-color: #002550;
    z-index: -1;
    animation: grow 7s infinite linear;
}
.service-card::after {
    position: absolute;
    content: '';
    bottom: 0;
    left: 0;
    width: 100%;
    height: 82%;
    clip-path: polygon(46% 66%, 76% 64%, 100% 41%, 100% 100%, 0 100%, 0 79%, 26% 79%);
    background-color: #004393;
    z-index: -2;
    animation: grow 7s infinite linear;
}
.service-card .dotted-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    background-image: radial-gradient(#fff 20%, transparent 0), radial-gradient(#fff 20%, transparent 0);
    background-size: 10px 10px;
    background-position: 0 0, 25px 25px;
    opacity: 0.08;
    z-index: -1;
}
.service-card__icon {
    font-size: 66px;
    line-height: 1;
    color: #4582ff;
}
.service-card__content .title {
    color: #fff;
}
.service-card__content p {
    color: #f2f2f2;
}
@-webkit-keyframes grow {
    0% {
        clip-path: polygon(46% 66%, 76% 64%, 100% 41%, 100% 100%, 0 100%, 0 79%, 26% 79%);
    }
    25% {
        clip-path: polygon(48% 70%, 76% 63%, 100% 41%, 100% 100%, 0 100%, 0 78%, 25% 81%);
    }
    50% {
        clip-path: polygon(51% 67%, 80% 71%, 100% 45%, 100% 100%, 0 100%, 0 78%, 30% 79%);
    }
    75% {
        clip-path: polygon(48% 70%, 76% 63%, 100% 41%, 100% 100%, 0 100%, 0 78%, 25% 81%);
    }
    100% {
        clip-path: polygon(46% 66%, 76% 64%, 100% 41%, 100% 100%, 0 100%, 0 79%, 26% 79%);
    }
}
@-moz-keyframes grow {
    0% {
        clip-path: polygon(46% 66%, 76% 64%, 100% 41%, 100% 100%, 0 100%, 0 79%, 26% 79%);
    }
    25% {
        clip-path: polygon(48% 70%, 76% 63%, 100% 41%, 100% 100%, 0 100%, 0 78%, 25% 81%);
    }
    50% {
        clip-path: polygon(51% 67%, 80% 71%, 100% 45%, 100% 100%, 0 100%, 0 78%, 30% 79%);
    }
    75% {
        clip-path: polygon(48% 70%, 76% 63%, 100% 41%, 100% 100%, 0 100%, 0 78%, 25% 81%);
    }
    100% {
        clip-path: polygon(46% 66%, 76% 64%, 100% 41%, 100% 100%, 0 100%, 0 79%, 26% 79%);
    }
}
@-ms-keyframes grow {
    0% {
        clip-path: polygon(46% 66%, 76% 64%, 100% 41%, 100% 100%, 0 100%, 0 79%, 26% 79%);
    }
    25% {
        clip-path: polygon(48% 70%, 76% 63%, 100% 41%, 100% 100%, 0 100%, 0 78%, 25% 81%);
    }
    50% {
        clip-path: polygon(51% 67%, 80% 71%, 100% 45%, 100% 100%, 0 100%, 0 78%, 30% 79%);
    }
    75% {
        clip-path: polygon(48% 70%, 76% 63%, 100% 41%, 100% 100%, 0 100%, 0 78%, 25% 81%);
    }
    100% {
        clip-path: polygon(46% 66%, 76% 64%, 100% 41%, 100% 100%, 0 100%, 0 79%, 26% 79%);
    }
}
@keyframes grow {
    0% {
        clip-path: polygon(46% 66%, 76% 64%, 100% 41%, 100% 100%, 0 100%, 0 79%, 26% 79%);
    }
    25% {
        clip-path: polygon(48% 70%, 76% 63%, 100% 41%, 100% 100%, 0 100%, 0 78%, 25% 81%);
    }
    50% {
        clip-path: polygon(51% 67%, 80% 71%, 100% 45%, 100% 100%, 0 100%, 0 78%, 30% 79%);
    }
    75% {
        clip-path: polygon(48% 70%, 76% 63%, 100% 41%, 100% 100%, 0 100%, 0 78%, 25% 81%);
    }
    100% {
        clip-path: polygon(46% 66%, 76% 64%, 100% 41%, 100% 100%, 0 100%, 0 79%, 26% 79%);
    }
}
/* service section css end */
/* why choose us section css start */
.choose-card {
    padding: 1.875rem;
    background-color: #fff;
    box-shadow: 0 3px 5px rgba(0, 32, 70, 0.15);
    border: 2px solid #e5e5e5;
    height: 100%;
}
.choose-card:hover {
    box-shadow: 0 5px 15px rgba(0, 32, 70, 0.15);
    -webkit-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    transform: translateY(-5px);
}
.choose-card__icon img {
    height: 65px;
}
.choose-card__content {
    margin-top: 2.1875rem;
}
/* why choose us section css end */
/* feature section css start */
.feaure-card:hover img {
    animation: fadeInUp 0.3s linear;
}
.feaure-card__icon img {
    height: 65px;
}
/* feature section css end */
/* business section css start */
.coutomer-item {
    padding-top: 3.125rem;
    padding-bottom: 3.125rem;
    border: 1px solid #e5e5e5;
    margin-top: -1px;
    margin-left: -1px;
    text-align: center;
}
@media (max-width: 480px) {
    .coutomer-item {
        padding-top: 1.875rem;
        padding-bottom: 1.875rem;
    }
}
.coutomer-item:nth-child(-n + 3) {
    border-top: none;
}
.coutomer-item:nth-last-child(-n + 3) {
    border-bottom: none;
}
.coutomer-item:nth-of-type(3n + 1) {
    border-left: none;
}
.coutomer-item:nth-of-type(3n + 3) {
    border-right: none;
}
/* business section css end */
/* overview section css start */
.overview-section {
    padding-top: 9.375rem;
    padding-bottom: 7.5rem;
}
@media (max-width: 1199px) {
    .overview-section {
        padding-top: 6.25rem;
        padding-bottom: 6.25rem;
    }
}
.overview-single .overview-number {
    font-size: 2.625rem;
}
@media (max-width: 575px) {
    .overview-single .overview-number {
        font-size: 2rem;
    }
}
.overview-single .caption {
    font-size: 1rem;
}
@media (max-width: 575px) {
    .overview-single .caption {
        font-size: 0.875rem;
    }
}
/* overview section css end */
/* testimonial section css start */
.testimonial-slide-area .thumb {
    width: 70%;
}
@media (max-width: 575px) {
    .testimonial-slide-area .thumb {
        width: 100%;
    }
}
.testimonial-slide-area .content {
    width: 60%;
    background-color: #002046;
    padding: 3.125rem;
    color: #fff;
    margin-left: auto;
    margin-top: -150px;
    position: relative;
    z-index: 1;
    box-shadow: 0 10px 35px rgba(0, 32, 70, 0.15);
}
@media (max-width: 991px) {
    .testimonial-slide-area .content {
        margin-top: -50px;
        width: 80%;
    }
}
@media (max-width: 575px) {
    .testimonial-slide-area .content {
        width: 100%;
        margin-top: -30px;
    }
}
@media (max-width: 480px) {
    .testimonial-slide-area .content {
        padding: 2.5rem 1.875rem;
    }
}
.testimonial-slide-area .content::after {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    background-image: radial-gradient(#fff 20%, transparent 0), radial-gradient(#fff 20%, transparent 0);
    background-size: 10px 10px;
    background-position: 0 0, 25px 25px;
    opacity: 0.08;
    z-index: -1;
}
.testimonial-slide-area .content-slider .slick-arrow {
    width: 60px;
    height: 55px;
    background-color: #4582ff;
    color: #fff;
    font-size: 24px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 100px;
}
@media (max-width: 991px) {
    .testimonial-slide-area .content-slider .slick-arrow {
        top: 0;
    }
}
@media (max-width: 575px) {
    .testimonial-slide-area .content-slider .slick-arrow {
        top: -75px;
    }
}
.testimonial-slide-area .content-slider .slick-arrow.prev {
    left: -170px;
}
@media (max-width: 575px) {
    .testimonial-slide-area .content-slider .slick-arrow.prev {
        right: 59px;
        left: auto;
    }
}
.testimonial-slide-area .content-slider .slick-arrow.next {
    left: -110px;
}
@media (max-width: 575px) {
    .testimonial-slide-area .content-slider .slick-arrow.next {
        right: 0;
        left: auto;
    }
}
/* testimonial section css end */
/* blog-card css start */
.blog-card {
    padding: 0.625rem 0.625rem 1.25rem 0.625rem;
    background-color: #fff;
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    height: 100%;
}
.blog-card__thumb {
    max-height: 295px;
    overflow: hidden;
}
.blog-card__thumb img {
    width: 100%;
}
.blog-card .post-time {
    background-color: #4582ff;
    display: inline-block;
    text-align: center;
    padding: 0.3125rem;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
}
.blog-card .post-time .post-date {
    display: block;
    color: #fff;
    font-family: "Maven Pro", sans-serif;
    font-weight: 500;
    font-size: 1.75rem;
    line-height: 1;
    padding-bottom: 0.375rem;
}
.blog-card .post-time .post-month {
    color: #fff;
    border-top: 1px solid #e5e5e5;
    padding-top: 0.375rem;
    line-height: 1;
}
.blog-card__meta {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.625rem;
    margin-top: -85px;
}
.blog-card__meta .post-time {
    width: 65px;
}
.blog-card__meta .meta {
    width: calc(100% - 95px);
    border-top: 1px solid #e5e5e5;
    border-bottom: 1px solid #e5e5e5;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.blog-card__meta .meta li {
    margin: 0.3125rem 0.5rem;
}
.blog-card__meta .meta a {
    color: #464646;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
}
.blog-card__meta .meta a i {
    font-size: 1.125rem;
    margin-right: 0.3125rem;
}
.blog-card__content {
    padding: 0 0.625rem;
    margin-top: 2rem;
}
/* blog-card css end */

/* blog details section css start */
.blog-details-title {
    font-size: 2.625rem;
}

@media (max-width: 1399px) {
    .blog-details-title {
        font-size: 2.25rem;
    }
}

@media (max-width: 991px) {
    .blog-details-title {
        font-size: 2rem;
    }
}

@media (max-width: 575px) {
    .blog-details-title {
        font-size: 1.75rem;
    }
}

.post-share {
    padding: 1.25rem 0;
    border-top: 1px solid rgba(0, 0, 0, 0.15);
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
    margin: -0.1875rem -0.3125rem;
}

.post-share li {
    padding: 0.1875rem 0.3125rem;
}

.post-share li a {
    width: 2.1875rem;
    height: 2.1875rem;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #777;
}

.blog-sidebar {
    padding: 1.25rem;
}

.blog-sidebar .title {
    position: relative;
    padding-bottom: 0.3125rem;
    margin-bottom: 1.875rem;
}

.blog-sidebar .title::after {
    position: absolute;
    content: '';
    bottom: 0;
    left: 0;
    width: 2.1875rem;
    height: 0.125rem;
}

.s-post {
    padding: 0.9375rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.s-post:first-child {
    padding-top: 0;
}

.s-post:last-child {
    padding-bottom: 0;
    border-bottom: none !important;
}

.s-post__thumb {
    width: 5rem;
    height: 4.375rem;
}

.s-post__thumb img {
    width: 100%;
    height: 100%;
}

.s-post__content {
    width: calc(100% - 80px);
    padding-left: 1.25rem;
}

.s-post__title {
    font-size: 1rem;
}

.s-post__title a {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* blog details section css end */


/* account section css start */
.account-section {
    min-height: 100vh;
    display: flex;
    flex-wrap: wrap;
    min-height: 100vh;
}

.account-section .left {
    width: 480px;
    padding: 100px 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.account-section .site-logo img {
    max-width: 250px;
}

.account-section .right {
    width: calc(100% - 480px);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 1;
}

.account-section .right::before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #002046;
    opacity: 0.45;
    z-index: -1;
}

.account-section .right .title {
    font-size: 38px;
    color: #fff;
}

@media (max-width: 1199px) {
    .account-section .left  {
        width: 400px;
    }
    .account-section .right {
        width: calc(100% - 400px);
    }
}

@media (max-width: 767px) {
    .account-section .left  {
        width: 100%;
        padding: 70px 40px;
    }
    .account-section .right {
        display: none ;
    }
}

@media (max-width: 575px) {
    .account-section .left  {
        padding: 40px 30px;
    }
}

@media (max-width: 380px) {
    .account-section .left  {
        padding: 40px 20px;
    }
}

.account-section .site-logo {
    width: 100px;
    height: 100px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
}

.account-section.style--two .left {
    width: 40%;
}
.account-section.style--two .right {
    width: 60%;
}

@media (max-width: 1599px) {
    .account-section.style--two .left {
        width: 50%;
    }
    .account-section.style--two .right {
        width: 50%;
    }
}

@media (max-width: 991px) {
    .account-section.style--two .left {
        width: 100%;
    }
    .account-section.style--two .right {
        display: none;
    }
}

.account-wrapper {
    background-color: #fff;
    box-shadow: 0 5px 25px 1px rgba(0, 0, 0, 0.15);
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
    overflow: hidden;
}
.account-wrapper .left {
    width: 45%;
}
@media (max-width: 991px) {
    .account-wrapper .left {
        display: none;
    }
}
.account-wrapper .right {
    width: 55%;
    padding: 4.375rem 3.125rem;
}

.account-section .account-wrapper .right {
    padding: 50px 40px;
}

@media (max-width: 991px) {
    .account-wrapper .right {
        width: 100%;
    }
}
@media (max-width: 480px) {
    .account-wrapper .right {
        padding: 3.125rem 1.875rem;
    }
    .account-section .account-wrapper .right {
        padding: 30px 15px;
    }   
}
@media (max-width: 380px) {
    .account-wrapper .right {
        padding: 1.875rem 0.9375rem;
    }
}
.account-wrapper .right .inner {
    width: 80%;
    margin: 0 auto;
}
@media (max-width: 1199px) {
    .account-wrapper .right .inner {
        width: 90%;
    }
}
@media (max-width: 767px) {
    .account-wrapper .right .inner {
        width: 100%;
    }
}
.user-account-check {
    position: relative;
    cursor: pointer;
}
.user-account-check .form-check-input {
    position: absolute;
    top: 10px;
    right: 20px;
}
.user-account-check .form-check-input:checked ~ label {
    border-color: #4582ff;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.08);
}
.user-account-check .form-check-input:checked ~ label i {
    color: #4582ff;
}
.user-account-check label {
    display: block;
    padding: 1.25rem;
    background-color: #fff;
    border: 2px solid #e5e5e5;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.user-account-check label i {
    font-size: 4.5rem;
    color: #939393;
}
.user-account-check label span {
    display: block;
}
/* account section css end */
/* contact section css start */
.single-info__icon {
    width: 5.3125rem;
    height: 5.3125rem;
    font-size: calc(5.3125rem / 2);
}
@media (max-width: 991px) {
    .single-info__icon {
        width: 4.6875rem;
        height: 4.6875rem;
    }
}
@media (max-width: 380px) {
    .single-info__icon {
        width: 3.75rem;
        height: 3.75rem;
        font-size: calc(3.75rem / 2);
    }
}
.single-info__content {
    width: calc(100% - 5.3125rem);
    padding-left: 1.5625rem;
}
.single-info__content .title {
    font-size: 1.75rem;
}
@media (max-width: 991px) {
    .single-info__content .title {
        font-size: 1.5rem;
    }
}
@media (max-width: 380px) {
    .single-info__content .title {
        font-size: 1.375rem;
    }
}
.contact-info-list .single-info {
    padding: 1.25rem 0;
}
.contact-info-list .single-info:first-child {
    padding-top: 0;
}
.contact-info-list .single-info:last-child {
    padding-bottom: 0;
}
/* contact section css end */
/* support section css start */
.custom--file-upload {
    position: relative;
    line-height: 2rem;
}
.custom--file-upload::before {
    position: absolute;
    content: 'Choose File';
    color: #fff;
    top: 0;
    left: 0;
    width: 125px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #4582ff;
    padding: 0.625rem 1.25rem;
}
.single-reply {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
    padding: 1.875rem;
    background-color: rgba(69, 130, 255, 0.03);
    border: 1px solid rgba(69, 130, 255, 0.6);
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.single-reply .left {
    width: 20%;
}
@media (max-width: 767px) {
    .single-reply .left {
        width: 100%;
    }
}
.single-reply .right {
    width: 80%;
    border-left: 1px solid rgba(69, 130, 255, 0.25);
    padding-left: 1.25rem;
}
@media (max-width: 767px) {
    .single-reply .right {
        width: 100%;
        padding-left: 0;
        border-left: none;
        margin-top: 1.25rem;
    }
}
.single-reply + .single-reply {
    margin-top: 20px;
}
/* support section css end */
/* authentication section css start */
.qr-code-wrapper {
    padding: 1.25rem;
    background-color: #002046;
}
.qr-code {
    padding: 0.625rem 1.25rem;
    background-color: #002046;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
}
.qr-code-copy-form {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.qr-code-copy-form input[type="text"] {
    background-color: transparent;
    border: none;
    color: #ffffff;
    font-size: 1.25rem;
    width: calc(100% - 75px);
    height: 40px;
}
@media (max-width: 440px) {
    .qr-code-copy-form input[type="text"] {
        font-size: 1rem;
    }
}
@media (max-width: 360px) {
    .qr-code-copy-form input[type="text"] {
        font-size: 0.875rem;
    }
}
.qr-code-copy-form input[type="submit"] {
    width: 75px;
    background-color: #4582ff;
    border: none;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    color: #fff;
}
.qr-code-form {
    position: relative;
}
.qr-code-form .form-control {
    height: 4.0625rem;
    padding-right: 5.9375rem;
}
.qr-code-form__btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 75px;
    height: calc(100% - 20px);
    color: #fff;
}
/* authentication section css end */
/* footer section css start */
.footer {
    background-color: #002046;
}
.footer__cta {
    padding-top: 4.375rem;
}
.footer__bottom {
    padding: 0.9375rem 0;
    background-color: #001937;
}
.cta-wrapper {
    padding: 3.125rem 4.375rem;
    background-color: #4582ff;
    overflow: hidden;
    position: relative;
    z-index: 1;
}
@media (max-width: 1199px) {
    .cta-wrapper {
        padding: 2.5rem 2.5rem;
    }
}
@media (max-width: 575px) {
    .cta-wrapper {
        padding: 1.875rem 0.9375rem;
    }
}
.cta-wrapper::before {
    position: absolute;
    content: '';
    top: 50%;
    left: -120px;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 350px;
    height: 350px;
    border: 1px solid #fff;
    background-color: transparent;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    opacity: 0.5;
    z-index: -1;
}
.cta-wrapper::after {
    position: absolute;
    content: '';
    top: -40px;
    right: -60px;
    width: 200px;
    height: 200px;
    background: transparent;
    background-image: radial-gradient(#fff 20%, transparent 0), radial-gradient(#fff 20%, transparent 0);
    background-size: 10px 10px;
    background-position: 0 0, 25px 25px;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    z-index: -1;
}
.footer-contact-area {
    position: relative;
    z-index: 1;
}
.footer-contact-area::before {
    position: absolute;
    content: '';
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.15);
    z-index: -1;
}
.footer-contact-wrapper {
    background-color: #002c60;
}
.footer-contact-item {
    padding: 1.25rem 1.5625rem;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    justify-content: center;
    position: relative;
}
.footer-contact-item:last-child::after, .footer-contact-item:last-child::before {
    display: none;
}
.footer-contact-item::after {
    position: absolute;
    content: '';
    top: 50%;
    right: 0;
    height: 50px;
    width: 1px;
    background-color: rgba(255, 255, 255, 0.15);
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}
.footer-contact-item::before {
    position: absolute;
    content: '';
    top: 50%;
    right: 1px;
    height: 50px;
    width: 1px;
    background-color: #00142d;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}
.footer-contact-card {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
@media (max-width: 991px) {
    .footer-contact-card {
        justify-content: center;
    }
}
.footer-contact-card:hover .icon {
    background-color: #4582ff;
}
.footer-contact-card:hover .icon::after {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}
.footer-contact-card .icon {
    width: 55px;
    height: 55px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(69, 130, 255, 0.55);
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    color: #fff;
    font-size: 1.625rem;
    position: relative;
}
.footer-contact-card .icon::after {
    position: absolute;
    content: '';
    top: -6px;
    left: -6px;
    width: calc(100% + (6px * 2));
    height: calc(100% + (6px * 2));
    background-color: rgba(69, 130, 255, 0.25);
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}
.footer-contact-card .content {
    width: calc(100% - 55px);
    padding-left: 20px;
}
@media (max-width: 991px) {
    .footer-contact-card .content {
        width: 100%;
        padding-left: 0;
        text-align: center;
        margin-top: 1.25rem;
    }
}
.footer-contact-card .content a {
    color: #fff;
}
.footer-contact-card .content a:hover {
    color: #4582ff;
}
.footer-contact-card .content .caption {
    color: #f2f2f2;
}
.widget-area {
    padding-top: 6.25rem;
    padding-bottom: 6.25rem;
}
.footer-logo img {
    max-height: 45px;
}
.footer-widget__title {
    position: relative;
    z-index: 1;
    padding-left: 25px;
    margin-bottom: 2.5rem;
}
@media (max-width: 1199px) {
    .footer-widget__title {
        padding-left: 15px;
    }
}
@media (max-width: 991px) {
    .footer-widget__title {
        margin-bottom: 1.25rem;
    }
}
.footer-widget__title::before {
    position: absolute;
    content: '';
    top: 13px;
    left: 0;
    width: 15px;
    height: 1px;
    background-color: #4582ff;
}
@media (max-width: 1199px) {
    .footer-widget__title::before {
        width: 10px;
    }
}
.footer-link-list li:first-child a {
    padding-top: 0;
    border-top: none;
}
.footer-link-list li:last-child a {
    padding-bottom: 0;
}
.footer-link-list li a {
    padding: 0.625rem 0;
    color: #fff;
    border-top: 1px dashed rgba(255, 255, 255, 0.15);
    display: block;
}
.footer-link-list li a:hover {
    color: #4582ff;
}
/* footer section css end */


.cookies-card {
    width: 520px;
    padding: 30px;
    color: #002046;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 999;
  }

  
  .cookies-card.bg--default {
    background-color: #EADFD8;
  }
  
  .cookies-card__icon {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    background-color: #DED4CD;
    font-size: 32px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }
  
  .cookies-card__content {
    margin-bottom: 0;
  }
  
  .cookies-btn {
    color: #363636;
    text-decoration: none;
    padding: 10px 25px;
    border: 1px solid #848484;
    margin: 3px 5px;
    display: inline-block;
  }
  
  .cookies-btn:hover {
    background-color: #DED4CD;
    color: #363636;
  }
  
  .cookies-btn {
    border-radius: 45px;
  }
  
  .cookies-card.style--lg {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
  
  .cookies-card.style--lg .cookies-card__content {
    width: calc(100% - (55px + 220px));
    padding: 0 25px;
  }
  .cookies-card.style--lg .cookies-card__btn {
    width: 220px;
  }
  
  
  .cookies-card.cookies--dark {
    color: #EADFD8;
    background-color: #002c60;
  }
  
  .cookies-card.cookies--dark .cookies-btn {
    color: #EADFD8;
  }
  
  .cookies-card.cookies--dark .cookies-btn:hover {
    background-color: #343A50;
    color: #EADFD8;
  }
  
  .cookies-card.cookies--dark .cookies-card__icon {
    background-color: #1E2337;
  }
  
  @media (max-width: 1399px) {
    .cookies-card.style--lg, .cookies-card  {
      width: 100%;
    }
  }
  
  @media (max-width: 767px) {
    .cookies-card.style--lg {
      display: block;
    }
    .cookies-card.style--lg .cookies-card__content {
      width: 100%;
      padding: 0;
      margin-top: 25px;
    }
    .cookies-card.style--lg .cookies-card__btn {
      width: 100%;
      margin-top: 20px;
    }
  }

/*# sourceMappingURL=main.css.map */

.main-menu li.active > a{
    color: #4582ff;
}

.responsive-text {
    max-width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
}



.verifiction-page.account-section .left {
    width: 50%;
    padding: 100px 50px;
    display: flex;
    justify-content: center;
    align-items: center;

}

.verifiction-page.account-section .right {
    width: calc(100% - 50%);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 1;
}
 @media (max-width: 1199px) {
    .verifiction-page.account-section .left  {
        width: 100%;
    }
    .verifiction-page.account-section .right {
       display: none;
    }
}
@media (max-width: 575px) {
    .verifiction-page.account-section .left  {
        padding: 0;
    }
}

/* Subscriber css Start  */

.subscribe-section .section-wave-img {
    width: 100%;
}
.subscribe-section .section-wave-img img {
    width: 100%;
}

.subscribe-section {
    position: relative;
    padding: 80px 0; 
}
@media (max-width: 575px) {
    .subscribe-section {
        padding: 60px 0; 
    }
}
.subscribe-section::before {
    position: absolute;
    content: "";
    width: 54%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: #002046;
    clip-path: polygon(0 0, 100% 0, 82% 100%, 0% 100%);
}
@media (max-width: 991px) {
    .subscribe-section::before {
    width: 100%;
    background-color: #002046c7;
    clip-path: unset;
}
}
.subscription-form {
border: 3px solid #4582ff;
border-radius: 40px;
position: relative;
overflow: hidden;
margin-left: 35px;
}   
@media (max-width: 991px) {
.subscription-form  {
margin-left: 0px;
margin-top: 30px;
}
}
.subscription-form .form--control {
background-color: #002046;
color: #fff;
border-radius: 0;
font-weight: 500;
border-radius: 40px;
padding: 35px 25px;
border: 0;     
padding-right: 177px;
border: none !important;
}
.subscription-form .form--control::placeholder {
color: rgba(255, 255, 255, 0.477) !important;
font-weight: 400;
font-size: 14px; 
}
.subscription-form .btn {
position: absolute;
right: 8px;
top: 50%;
transform: translateY(-50%);
display: flex;
height: auto;
align-items: center;
border-radius: 40px !important;
padding: 15px 30px;
font-size: 16px;
}
.btn-icon {
font-size: 15px; 
margin-left: 5px;
}
@media (max-width: 424px) { 
.btn-icon {
margin-left: 0px;
}
.btn-text {
display: none;
}
}

@media (max-width: 575px) {
.subscribe-content {
position: relative;
z-index: 2;
padding-right: 0px;
}
}
.subscribe-content {
position: relative;
z-index: 2;
padding-right: 55px;
}
@media (max-width: 575px) {
.subscribe-content {
position: relative;
z-index: 2;
padding-right: 0px;
}
}
.subscribe-content .section-title {
font-size: 44px;
}
@media (max-width: 767px) {
.subscribe-content .section-title {
font-size: 35px;
}
}
@media (max-width: 575px) {
.subscribe-content .section-title {
font-size: 30px;
}
}
/* Subcriber End */

/* Banned Start */
.card.banned {
    overflow: hidden;
    box-shadow: 0px 2px 8px 1px #60606024;
    border-width: 1px;
    border-radius: 8px;
}
.card.banned .card-title {
    color: #ea5455;
    margin-bottom: 0;
    font-size: 25px;
    text-shadow: 2px 3px 2px #ea545540;
}

.box-shadow {
    background-color: #fff;
    border: 1px solid #dddddd5c;
    box-shadow: 1px 1px 9px #00000012;
}

.box-shadow .card-header{
    background-color: #fff;
}