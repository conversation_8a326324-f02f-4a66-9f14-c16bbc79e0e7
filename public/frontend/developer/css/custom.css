/* Copy Animation */

.base-color{
  color: hsl(var(--main)) !important;
}

  .copyInput {
	display: inline-block;
	line-height: 50px;
	position: absolute;
	top: 0;
	right: 0;
	width: 40px;
	text-align: center;
	font-size: 14px;
	cursor: pointer;
	-webkit-transition: all .3s;
	-o-transition: all .3s;
	transition: all .3s;
  }
  
  .copied::after {
	position: absolute;
	top: 8px;
	right: 12%;
	width: 100px;
	display: block;
	content: "COPIED";
	font-size: 1em;
	padding: 5px 5px;
	color: #fff;
	background-color: #FF7000;
	border-radius: 3px;
	opacity: 0;
	will-change: opacity, transform;
	animation: showcopied 1.5s ease;
  }
  
  @keyframes showcopied {
	0% {
		opacity: 0;
		transform: translateX(100%);
	}
	50% {
		opacity: 0.7;
		transform: translateX(40%);
	}
	70% {
		opacity: 1;
		transform: translateX(0);
	}
	100% {
		opacity: 0;
	}
  }




  .cookies-card {
	width: 520px;
	padding: 30px;
	color: #ffffff;
	position:  fixed;
	bottom: 15px;  
	left: 15px;
	z-index: 999999;
	transition: all .5s;
	background: #002550;
    border-radius: 5px;
  }
  
  .cookies-card.hide{
	bottom: -500px !important;
  }
  .radius--10px {
	border-radius: 10px;
  }
  
  .cookies-card__icon {
	width: 55px;
	height: 55px;
	border-radius: 50%;
	background-color: #6e6f70;
    color: #fff;
	font-size: 32px;
	display: inline-flex;
	justify-content: center;
	align-items: center;
  }
  
  .cookies-card__content {
	margin-bottom: 0;
  }
  
  .cookies-btn {
	color: #363636;
	text-decoration: none;
	padding: 10px 35px;
	margin: 3px 5px;
	display: inline-block;
	border-radius:  999px;
  }
  
  .cookies-btn:hover {
	color: #363636;
  }

  
  @media (max-width: 767px) {
	  .cookies-card {
		  width:  100%;
		  left: 0;
		  bottom:  0;
		  font-size:  14px;
		  padding:  15px;
	  }
  }




.hover-input-popup {
        position: relative;
    }
    .input-popup {
        display: none;
    }
    .hover-input-popup .input-popup {
        display: block;
        position: absolute;
        bottom: 130%;
        left: 50%;
        width: 280px;
        background-color: #1a1a1a;
        color: #fff;
        padding: 20px;
        border-radius: 5px;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        -ms-border-radius: 5px;
        -o-border-radius: 5px;
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%);
        -webkit-transition: all 0.3s;
        -o-transition: all 0.3s;
        transition: all 0.3s;
    }
    .input-popup::after {
        position: absolute;
        content: '';
        bottom: -19px;
        left: 50%;
        margin-left: -5px;
        border-width: 10px 10px 10px 10px;
        border-style: solid;
        border-color: transparent transparent #1a1a1a transparent;
        -webkit-transform: rotate(180deg);
        -ms-transform: rotate(180deg);
        transform: rotate(180deg);
    }
    .input-popup p {
        padding-left: 20px;
        position: relative;
    }
    .input-popup p::before {
        position: absolute;
        content: '';
        font-family: 'Line Awesome Free';
        font-weight: 900;
        left: 0;
        top: 4px;
        line-height: 1;
        font-size: 18px;
    }
    .input-popup p.error {
        text-decoration: line-through;
    }
    .input-popup p.error::before {
        content: "\f057";
        color: #ea5455;
    }
    .input-popup p.success::before {
        content: "\f058";
        color: #28c76f;
    }



 .show-filter{
  display: none;
}
@media(max-width:767px){
  .responsive-filter-card{
      display: none;
      transition: none;
  }
  .show-filter{
      display: block;
  }
}