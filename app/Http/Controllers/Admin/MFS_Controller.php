<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\MfsOperator;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;

class MFS_Controller extends Controller
{
    public function index()
    {
        $data = MfsOperator::all();

        return view('admin.mfs_operator.index', compact('data'));
    }

    public function create_mfs()
    {
        return view('admin.mfs_operator.create_mfs');
    }

    public function insert_mfs(Request $request)
    {

        $validator = Validator::make($request->all(),[
            'mfs_name' => 'required',
            'mfs_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $check = mfsOperator::where('name', $request->mfs_name)->first();

        if ($check) {
            return Redirect::back()->with('alert', 'This name already exist.');
        } else {

            $data = new mfsOperator();

            if ($request->hasFile('mfs_logo')) {
                $image = $request->file('mfs_logo');
                $imageName = time() . '.' . $image->getClientOriginalExtension();
                $image->move(public_path('uploads'), $imageName);
                $data->image = 'uploads/'.$imageName;

            }

            if($request->mfs_status == 'off') {
                $data->status = 0;
            }


            $data->name = $request->mfs_name;





            if ($data->save()) {
                return redirect()->route('mfs.index');
            } else {
                return Redirect::back()->with('alert', 'Failed to save data.');
            }
        }
    }

    public function edit_mfs_view($id)
    {
        $data = mfsOperator::where('id', $id)->first();

        return view('admin.mfsOperator.edit_mfs', compact('data'));
    }

    public function edit_mfs(Request $request)
    {

        $validator = Validator::make($request->all(),[
            'mfs_name' => 'required',
            'mfs_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $check = mfsOperator::where('id', $request->mfs_id)->first();
        // @dd($check);
        if ($check->name == $request->mfs_name) {
            return Redirect::back()->with('alert', 'This name is similar with previous name.');
        } else {
            if ($request->hasFile('mfs_logo')) {
                $image = $request->file('mfs_logo');
                $imageName = time() . '.' . $image->getClientOriginalExtension();
                $image->move(public_path('uploads'), $imageName);
                $check->image = 'uploads/'.$imageName;

            }

            $check->name = $request->mfs_name;


            if ($check->save()) {
                return Redirect::route('mfs.index')->with('message', 'Data saved successfully.');
            }
            return Redirect::back()->with('alert', 'Failed to save data.');
        }
    }

    public function mfs_destroy(Request $request)
    {


        // Retrieve the record you want to delete
        $record = mfsOperator::find($request->id);

        // Delete the record
        $record->delete();

        // Perform any additional actions, such as displaying a success message

        return redirect()
            ->route('mfs.index')
            ->with('message', 'Record deleted successfully.');
    }

    public function status_update(Request $request){

        $data = mfsOperator::find($request->id);
        if($request->status== '0'){
            $data->status = 1;
        } else{
            $data->status = 0;
        }
        $data->save();

        return $request;

    }
}
