<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;

use App\Models\AdminRate;
use Illuminate\Http\Request;

class AdminRateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(AdminRate $adminRate)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AdminRate $adminRate)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AdminRate $adminRate)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AdminRate $adminRate)
    {
        //
    }
}
