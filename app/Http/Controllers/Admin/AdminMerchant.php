<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Session;
use Auth;
use Mail;

use App\Models\Merchant;
use App\Models\Transaction;


class AdminMerchant extends Controller
{

    public function index(Request $request)
{
    // $sort_by = $request->get('sortby') ?: 'id';
    $sort_type = $request->get('sorttype') ?: 'asc';
    $rows = $request->get('rows') ? $request->get('rows'): 50;

    $query = Merchant::where('db_status', 'live');

    if ($request->filled('member_code')) {
        $query->where('username', $request->member_code);
    }

    if ($request->filled('message')) {
        $query->where(function ($q) use ($request) {
            $q->where('fullname', 'LIKE', '%' . $request->message . '%')
              ->orWhere('email', 'LIKE', '%' . $request->message . '%')
              ->orWhere('mobile', 'LIKE', '%' . $request->message . '%');
        });
    }

    $data = $query->paginate($rows);

    if ($request->ajax()) {
        return view('admin.merchant.admin_merchant_data', compact('data'))->render();
    }

    return view('admin.merchant.admin_merchant_list', compact('data'));
}

	public function edit($id)
    {
        $user = Merchant::find($id);
        return view('admin.merchant.merchant_edit_form', ['user' => $user]);
    }


    public function update(Request $request, $id)
    {
        $request->validate([
            'fullname' => 'required|max:55',
            'email' => 'required',
            'mobile' => 'required',
            //'password' => 'required',
        ]);

        $user = Merchant::find($id);
        $user->fullname = $request->fullname;
        $user->email = $request->email;
        $user->mobile = $request->mobile;
        $user->deposit_status = $request->deposit_status == 'on' ? 1 : 0;
        $user->withdraw_status = $request->withdraw_status == 'on' ? 1 : 0;
        if($request->password) {
            $user->password = bcrypt($request->password);
        }
        $user->save();

        return redirect('/admin/merchantList')->with('message', 'Data Update Success');
    }

    public function merchantAdd(Request $request)
    {
		return view('admin.merchant.merchant_add_form');
	}

	public function AddAction(Request $request)
    {

			DB::beginTransaction();

			$validatedData = $request->validate([
			'email' => 'required|string|email|unique:merchants|max:255',
            'password' => 'required|string|min:6',
           // 'pin_code' => 'required|string|min:4',
			'mobile' => 'required|unique:merchants|max:255',
			//'username' => 'required|unique:users|min:6|max:255',
            'fullname' => ['required', 'regex:/^[A-ZÀÂÇÉÈÊËÎÏÔÛÙÜŸÑÆŒa-zàâçéèêëîïôûùüÿñæœ0-9_.,() ]+$/'],
			]);

			$authid = auth()->user()->id;
			$member_code = rand(1111,9999);
			$user_type ='general';
			$password = $request->password;

		 $usercreate = Merchant::create([
            'fullname' => $request->fullname,
            'username' => $member_code,
            'mobile' => $request->mobile,
            'email' => $request->email,
            'password' => bcrypt($password),
            'merchant_type' => $user_type,
            'create_by' => $authid,
            'status' => 1,
            'email_verification_token' => Str::random(32)
        ]);


		 $mlMessage = "Dear ".$request->fullname. " Thanks for your registration in Your Merchant ID is : " . $member_code . " password is : " . $password . " And Login Mobile is : " . $request->mobile . "";

		 if(($usercreate)){

            DB::commit();

		 }else {
			  DB::rollback();
		 }

		  Session::flash('message', translate('merchant_created_successfully').' '.$mlMessage);

		  return redirect()->back()->withMsg(translate('merchant_created_successfully'));



	}

    public function delete($id)
    {

		$user = Merchant::find($id);
		$user->db_status = 'deleted';
		$user->save();
        //User::where('user_type', 'partner')->where('id', $id)->delete();

        return response()->json(['message' => " success "], 200);
    }

    public function merchant_add_balance(Request $request,$id)
    {   




		$validatedData = $request->validate([
			'amount' => 'required|min:1',
            'pincode' => 'required']);

			$amount = $request->amount;
			$pincode = $request->pincode;
			$details = $request->details;
			$balance_type = $request->balance_type;

            $user = auth('admin')->user();

            

            if($user->pincode != $pincode){                
                Session::flash('alert', translate('Pin not matched'));
                return redirect()->back();
            }

			$merchn = Merchant::find($id);
            $get_all_balance = getMerchantBalance($merchn->id);

            if($merchn->merchant_type == 'general'){

                if($balance_type == 'debit'){

                    if( $get_all_balance['availableBalance'] < $amount ){
                        Session::flash('alert', translate('Merchant balance is less than your amount'));
                        return redirect()->back();

                    }
                }

            }

            $oldbal = $get_all_balance['balance'];
            $new_bal = $oldbal['balance'] - $amount;

			$trx = rand(11111111,99999999);

			DB::beginTransaction();

			$trxcrt = Transaction::create([
					'user_id' => $id,
					'amount' => $amount,
					'charge' => 0,
					'old_balance' => $oldbal,
					'trx_type' => $balance_type,
					'trx' => $trx,
					'details' => $details,
					'user_type' => $merchn->merchant_type == "general"? "merchant" : "sub_merchant",
					'wallet_type' => 'admin',
                    'creator_id'=>$user->id,
                    'creator_type'=>'admin'
				]);
			$updatebal = Merchant::where('id',$id)->update(['balance'=>$new_bal]);

		if(($trxcrt) && ($updatebal)){

            DB::commit();

			Session::flash('message', translate('balance_update_successfully'));

		 }else {

			  DB::rollback();
			  Session::flash('alert', translate('not_work'));
		 }



		return redirect()->back();

	}

}
