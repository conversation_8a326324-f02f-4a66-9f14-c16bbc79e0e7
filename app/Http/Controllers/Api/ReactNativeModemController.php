<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Str;

use App\Models\smsInbox;
use App\Models\Modem;
use App\Models\User;
use App\Models\Merchant;
use App\Models\BalanceManager;
use App\Models\SmsSetting;
use App\Models\ServiceRequest;
use Illuminate\Support\Facades\Log;

class ReactNativeModemController extends Controller
{
    public function sendSmsToServer(Request $request)
    {
        // return response()->json([
        //     'message' => $request->all(),
        //     'status' => 'test',
        //     'success' => false
        // ]);


        $getoperator = $request->operator;
        $msg = $request->smsbody;
        $sendcode = $request->sender;
        $simid = $request->simid;
        $deviceid = $request->deviceid;
        $membercode = $request->membercode;
        $simslot = $request->simslot;
        $token = $request->token;
        $request_time = $request->smstime;
        $simnumber = $request->simnumber;

        if ($simslot == 0) {
            $tersimslot = 0;
            $dbinsertsim = 1;
        } else {
            $tersimslot = 1;
            $dbinsertsim = 2;
        }

        DB::beginTransaction();

        if (empty($membercode)) {
            return response()->json([
                'message' => 'Member id empty',
                'status_code' => 500,
                'success' => false
            ]);
        }

        if (empty($deviceid)) {
            return response()->json([
                'message' => 'Device id empty',
                'status_code' => 500,
                'success' => false
            ]);
        }

        if (empty($token)) {
            return response()->json([
                'message' => 'Token id empty',
                'status_code' => 500,
                'success' => false
            ]);
        }

        if (empty($msg)) {
            return response()->json([
                'message' => 'Message Body empty',
                'status_code' => 500,
                'success' => false
            ]);
        }

        if (empty($sendcode)) {
            return response()->json([
                'message' => 'Sendcode id empty',
                'status_code' => 500,
                'success' => false
            ]);
        }


        $getsms_time = date('Y-m-d H:i:s', $request_time / 1000);
        // $getsms_time = now();
        $idate = date('Y-m-d');

        $aget_udata = User::where('member_code', $membercode)->first();

        $user_id = $aget_udata->id;
        $partner_id = $aget_udata->partner;
        $dso_id = $aget_udata->dso;


        $modem_data = Modem::where('type', 'android')
            ->where('deviceid', $deviceid)
            ->where('simslot', $tersimslot)
            ->where('member_code', $membercode)
            ->first();

        //return "deviceid". $deviceid .", dbinsertsim". $dbinsertsim .", membercode". $membercode;
        //return $modem_data->id;

        $modem_id = $modem_data->id;
        $sim_number = $modem_data->sim_number;
        $merchant_code = $modem_data->merchant_code;

        $sms_exist = smsInbox::where('sms', $msg)->count();

        if ($sms_exist == 0) {
            //$agent_id = $aget_udata->id;

            $smsinbxcrt = smsInbox::create([
                'sender' => $sendcode,
                'sms' => $msg,
                'member_code' => $membercode ? $membercode : 'not_member',
                'modem' => $modem_id,
                'sim_slot' => $dbinsertsim ? $dbinsertsim : $simslot,
                'device_id' => $deviceid,
                'sms_time' => $getsms_time,
                'sim_number' => $sim_number ? $sim_number : $simnumber,
                'merchant_id' => $merchant_code ? $merchant_code : '-1',
                'token' => $token,
                'partner' => $partner_id ? $partner_id : '-1',
                'dso' => $dso_id ? $dso_id : '-1',
                'agent' => $user_id ? $user_id : '-1',
            ]);

            // $mtupdate = Modem::where('type', 'android')
            //     ->where('deviceid', $deviceid)
            //     ->where('member_code', $membercode)
            //     ->update(['up_time' => time()]);
        }


        if (empty($simnumber) || empty($sim_number)) {
            return response()->json([
                'message' => 'Sim number id empty',
                'status_code' => 500,
                'success' => false
            ]);
        }

        //        else {
//            $sim_number = 'testagentnumber';
//        }

        $smsbodytype = '';


        /// balance manager full function start here

        if ($sendcode == 'NAGAD' or $sendcode == 'Nagad' or $sendcode == 'bKash' or $sendcode == '16216' or  $sendcode == 'upay') {
            $responseValue = explode(':', $msg);

            //Cash Out Received.Amount: Tk 2100.00Customer: 01672151119TxnID: 71NHMIC5Comm: Tk 8.61Balance: Tk 110060.8121/01/2023 18:30
            //Cash In Successful.Amount: Tk 3000.00 Customer: 01827147299 TxnID: 71R8DC4R Comm: Tk 12.30 Balance: Tk 10980.99 12/03/2023 22:06

            if (str_contains($responseValue[0], 'Cash Out') && ($sendcode == 'NAGAD' || $sendcode == 'Nagad')) {
                $smsbodytype = 'ngcashout';
                $baltype = 'plus';
                $amount = floatval(str_replace(',', '', getStringBetween($msg, 'Tk ', 'Customer')));
                $number = trim(getStringBetween($msg, 'Customer: ', 'TxnID'));
                $trxid = trim(getStringBetween($msg, 'TxnID: ', 'Comm:'));

                $comm = trim(getStringBetween($msg, 'Comm: Tk ', 'Balance'));
                $comm = floatval(str_replace(',', '', $comm));

                $lastbal = trim(getStringBetween($msg, 'Balance: Tk ', ''));
                $lastbal = substr($lastbal, 0, -16);
                $lastbal = floatval(str_replace(',', '', $lastbal));

                $date = substr($msg, -16, strlen($msg));
                $hours = substr($date, 11, 2);
                $sms_date = $hours > 12 ? Carbon::createFromFormat("d/m/Y H:i", $date)->format("Y-m-d H:i") : Carbon::createFromFormat("d/m/Y h:i", $date)->format("Y-m-d H:i");
            }

            // nagad cashin

            if (str_contains($responseValue[0], 'Cash In') && ($sendcode == 'NAGAD' || $sendcode == 'Nagad')) {
                $smsbodytype = 'ngcashin';
                $baltype = 'minus';
                $amount = floatval(str_replace(',', '', getStringBetween($msg, 'Tk ', 'Customer')));
                $number = trim(getStringBetween($msg, 'Customer: ', 'TxnID'));
                $trxid = trim(getStringBetween($msg, 'TxnID: ', 'Comm:'));

                $comm = trim(getStringBetween($msg, 'Comm: Tk ', 'Balance'));
                $comm = floatval(str_replace(',', '', $comm));

                $lastbal = trim(getStringBetween($msg, 'Balance: Tk ', ''));
                $lastbal = substr($lastbal, 0, -16);
                $lastbal = floatval(str_replace(',', '', $lastbal));

                $date = substr($msg, -16, strlen($msg));
                $hours = substr($date, 11, 2);
                $sms_date = $hours > 12 ? Carbon::createFromFormat("d/m/Y H:i", $date)->format("Y-m-d H:i") : Carbon::createFromFormat("d/m/Y h:i", $date)->format("Y-m-d H:i");
            }


            // naga B2B
            // B2B Transfer Successful. Amount: Tk 3000.00 Receiver: 01810030342 TxnID: 71R9VMKW Balance: Tk 43462.02 13/03/2023 17:30

            if (str_contains($responseValue[0], 'B2B Transfer') && ($sendcode == 'NAGAD' || $sendcode == 'Nagad')) {
                $smsbodytype = 'ngB2BTR';
                $baltype = 'minus';
                $amount = floatval(str_replace(',', '', getStringBetween($msg, 'Tk ', 'Receiver')));
                $number = trim(getStringBetween($msg, 'Receiver: ', 'TxnID'));
                $trxid = trim(getStringBetween($msg, 'TxnID: ', 'Balance:'));

                // $comm = trim(getStringBetween($msg, 'Comm: Tk ', 'Balance'));
                $comm = 00;

                $lastbal = trim(getStringBetween($msg, 'Balance: Tk ', ''));
                $lastbal = substr($lastbal, 0, -16);
                $lastbal = floatval(str_replace(',', '', $lastbal));

                $date = substr($msg, -16, strlen($msg));
                $hours = substr($date, 11, 2);
                $sms_date = $hours > 12 ? Carbon::createFromFormat("d/m/Y H:i", $date)->format("Y-m-d H:i") : Carbon::createFromFormat("d/m/Y h:i", $date)->format("Y-m-d H:i");
            }


            // nagad b2b receive
            //B2B Received. Amount: Tk 3000.00 Sender: 01810030342 TxnID: 71R9OL1C Balance: Tk 16202.72 13/03/2023 16:05

            if (str_contains($responseValue[0], 'B2B Received') && ($sendcode == 'NAGAD' || $sendcode == 'Nagad')) {
                $smsbodytype = 'ngB2BRC';
                $baltype = 'plus';
                $amount = floatval(str_replace(',', '', getStringBetween($msg, 'Tk ', 'Sender')));
                $number = trim(getStringBetween($msg, 'Sender: ', 'TxnID'));
                $trxid = trim(getStringBetween($msg, 'TxnID: ', 'Balance:'));

                // $comm = trim(getStringBetween($msg, 'Comm: Tk ', 'Balance'));
                $comm = 00;

                $lastbal = trim(getStringBetween($msg, 'Balance: Tk ', ''));
                $lastbal = substr($lastbal, 0, -16);
                $lastbal = floatval(str_replace(',', '', $lastbal));

                $date = substr($msg, -16, strlen($msg));
                $hours = substr($date, 11, 2);
                $sms_date = $hours > 12 ? Carbon::createFromFormat("d/m/Y H:i", $date)->format("Y-m-d H:i") : Carbon::createFromFormat("d/m/Y h:i", $date)->format("Y-m-d H:i");
            }


            //Cash Out Tk 785.00 from 01841721504 successful. Fee Tk 0.00. Balance Tk 121,710.33. TrxID AA24AJJH6I at 02/01/2023 22:05

            //Congratulations! You have received Cashback Tk 1.00. Balance Tk 91,950.74. TrxID AC889WE2G0 at 08/03/2023 10:50


            if (str_contains($responseValue[0], 'Cash Out') && $sendcode == 'bKash') {
                $smsbodytype = 'bkcashout';
                $baltype = 'plus';
                $amount = floatval(str_replace(',', '', getStringBetween($msg, 'Tk ', ' from')));
                $number = trim(getStringBetween($msg, 'from ', ' success'));
                $trxid = trim(getStringBetween($msg, 'TrxID ', ' at'));
                $getcom = $amount * 0.4 / 100;
                //$comm = trim(getStringBetween($msg, 'Comm: Tk ', 'Balance'));
                $comm = $getcom;

                $lastbal = trim(getStringBetween($msg, 'Balance Tk ', '. TrxID'));

                $lastbal = floatval(str_replace(',', '', $lastbal));

                $date = substr($msg, -16, strlen($msg));
                $hours = substr($date, 11, 2);
                $sms_date = $hours > 12 ? Carbon::createFromFormat("d/m/Y H:i", $date)->format("Y-m-d H:i") : Carbon::createFromFormat("d/m/Y h:i", $date)->format("Y-m-d H:i");
            }

            //Cash In Tk 2,000.00 to 01856600204 successful. Fee Tk 0.00. Balance Tk 2,851.16. TrxID AC829VTZTE at 08/03/2023 10:37
            if (str_contains($responseValue[0], 'Cash In') && $sendcode == 'bKash') {
                $smsbodytype = 'bkcashin';
                $baltype = 'minus';
                $amount = floatval(str_replace(',', '', getStringBetween($msg, 'Cash In Tk ', ' to')));
                $number = trim(getStringBetween($msg, 'to ', ' success'));
                $trxid = trim(getStringBetween($msg, 'TrxID ', ' at'));
                $getcom = $amount * 0.4 / 100;
                $comm = $getcom;
                $lastbal = trim(getStringBetween($msg, 'Balance Tk ', '. TrxID'));

                $lastbal = floatval(str_replace(',', '', $lastbal));

                $date = substr($msg, -16, strlen($msg));
                $hours = substr($date, 11, 2);
                $sms_date = $hours > 12 ? Carbon::createFromFormat("d/m/Y H:i", $date)->format("Y-m-d H:i") : Carbon::createFromFormat("d/m/Y h:i", $date)->format("Y-m-d H:i");
            }


            // B2B Transfer Tk 2,000.00 to 01704172631 successful. Fee Tk 0.00. Balance Tk 7,521.24. TrxID ACD7FJDL8B at 13/03/2023 16:48

            if (str_contains($responseValue[0], 'B2B Transfer') && $sendcode == 'bKash') {
                $smsbodytype = 'bkB2B';
                $baltype = 'minus';
                $amount = floatval(str_replace(',', '', getStringBetween($msg, 'Transfer Tk ', ' to')));
                $number = trim(getStringBetween($msg, 'to ', ' success'));

                $trxid = trim(getStringBetween($msg, 'TrxID ', ' at'));
                //$getcom = $amount*0.4/100;
                $comm = 00;
                $lastbal = trim(getStringBetween($msg, 'Balance Tk ', '. TrxID'));

                $lastbal = floatval(str_replace(',', '', $lastbal));

                $date = substr($msg, -16, strlen($msg));
                $hours = substr($date, 11, 2);
                $sms_date = $hours > 12 ? Carbon::createFromFormat("d/m/Y H:i", $date)->format("Y-m-d H:i") : Carbon::createFromFormat("d/m/Y h:i", $date)->format("Y-m-d H:i");
            }

            // You have received Tk 2,000.00 from 01704172631. Fee Tk 0.00. Balance Tk 2,201.51. TrxID ACA8CBMVZ2 at 10/03/2023 16:36

            if (str_contains($responseValue[0], 'received') && $sendcode == 'bKash') {
                $smsbodytype = 'bkRC';
                $baltype = 'plus';
                $amount = floatval(str_replace(',', '', getStringBetween($msg, 'received Tk ', ' from')));
                $number = trim(getStringBetween($msg, 'from ', ' Fee'));

                $trxid = trim(getStringBetween($msg, 'TrxID ', ' at'));
                //$getcom = $amount*0.4/100;
                $comm = 00;
                $lastbal = trim(getStringBetween($msg, 'Balance Tk ', '. TrxID'));

                $lastbal = floatval(str_replace(',', '', $lastbal));

                $date = substr($msg, -16, strlen($msg));
                $hours = substr($date, 11, 2);
                $sms_date = $hours > 12 ? Carbon::createFromFormat("d/m/Y H:i", $date)->format("Y-m-d H:i") : Carbon::createFromFormat("d/m/Y h:i", $date)->format("Y-m-d H:i");
            }

            //Rocket B2C: Cash-Out from A/C: 017559717268 Tk500.00 Comm:Tk2.10; A/C Balance: Tk3,741.39.TxnId: 3478851618 Date:22-JAN-23 10:08:31 am. Download https://bit.ly/nexuspa
            if (str_contains($responseValue[1], 'Cash-Out') && $sendcode == '16216') {
                $smsbodytype = 'rccashout';
                $baltype = 'plus';
                $amount = floatval(str_replace(',', '', getStringBetween($msg, ' Tk', ' Comm')));
                $number = trim(getStringBetween($msg, 'A/C: ', ' Tk'));
                $trxid = trim(getStringBetween($msg, 'TxnId: ', ' Date:'));
                $lastbal = trim(getStringBetween($msg, 'Balance: Tk', '.TxnId:'));
                $lastbal = floatval(str_replace(',', '', $lastbal));

                $rocket_date = getStringBetween($msg, 'Date:', '. Download');
                // $sms_date = Carbon::createFromFormat("d-M-y h:i a", $rocket_date)->format("Y-m-d H:i");
                $sms_date = Carbon::parse($rocket_date)->format("Y-m-d H:i");

            }

            //upday cashout :  You have received Cash-out of Tk. 3940.00 from 01576987382. Comm: TK. 16.1343. Balance Tk. 6568.82. TrxID 01JYG4GET7 at 24/06/2025 10:52.

            if (str_contains($msg, 'Cash-out') && $sendcode == 'upay') {
                    $smsbodytype = 'upcashout';
                    $baltype = 'plus';
                    $trxtype = 'cashout';

                    // Extract amount
                    $amount = floatval(getStringBetween($msg, 'Cash-out of Tk. ', ' from'));

                    // Extract sender number
                    $number = getStringBetween($msg, 'from ', '. Comm');

                    // Extract transaction ID
                    $trxid = getStringBetween($msg, 'TrxID ', ' at');

                    // Extract balance
                    $lastbal = floatval(getStringBetween($msg, 'Balance Tk. ', '. TrxID'));

                   // Extract and format date
                    $dateStr = getStringBetween($msg, 'at ', '.'); // e.g., "24/06/2025 10:52"
                    
                    // Clean the date string (remove non-ASCII characters like &nbsp;)
                    $dateStr = preg_replace('/[^\x20-\x7E]/u', ' ', $dateStr);
                    $dateStr = trim($dateStr);

                    // Parse using exact format (handle leading zeros)
                    $sms_date = Carbon::createFromFormat('d/m/Y H:i', $dateStr)->format('Y-m-d H:i');

                }

            $bmcount = BalanceManager::where('sms_body', $msg)->count();

            if ($bmcount == 0) {
                $getbalancedata = BalanceManager::where('sender', $sendcode)
                    ->where('sim', $sim_number)
                    ->where('deviceid', $deviceid)
                    ->where('simslot', $dbinsertsim)
                    ->orderBy('id', 'desc')
                    ->first();

                $getbmlastbaldb = $getbalancedata->lastbal;

                $getbmlastbal = intval($getbmlastbaldb);

                if ($baltype == 'plus') {
                    $currentlastbal = $getbmlastbal + $amount + $comm;
                    $oldbalance = $getbmlastbaldb - ($amount + $comm);

                } else {
                    $currentlastbal = $getbmlastbal - ($amount + $comm);
                    $oldbalance = $getbmlastbaldb + $amount + $comm;
                }

                $courrentbmbalnumber = intval($currentlastbal);
                $defranceamount = $lastbal - $currentlastbal;

                $defran = intval($lastbal) - intval($currentlastbal);

                if ($courrentbmbalnumber == intval($lastbal)) {

                    $bmstst = 20;

                } elseif ($defran >= 1 && $defran <= 15) {

                    $bmstst = 20;

                } elseif ($defran >= 16 && $defran <= 500) {

                    $bmstst = 33;


                    // if($smsbodytype== 'ngcashout' && $membercode){
                    //     SendSMS('waiting',$membercode,$number,$trxid,$amount,$sms_date );
                    // };

                } elseif ($defran >= 501 && $defran <= 25000000) {

                    $bmstst = 55;

                    // if($smsbodytype== 'ngcashout' && $membercode){
                    //     SendSMS('danger',$membercode,$number,$trxid,$amount,$sms_date );
                    // };

                    // SendSMS('danger',$membercode,$number,$trxid,$amount,$sms_date );

                } elseif (empty($getbmlastbal)) {

                    $bmstst = 10;
                }



                $bmanger = BalanceManager::create([
                    'request_time' => $getsms_time,
                    'member_code' => $membercode ? $membercode : 'not_member',
                    'sender' => $sendcode,
                    'sim' => $sim_number ? $sim_number : $simnumber,
                    'oldbal' => $oldbalance,
                    'amount' => $amount,
                    'lastbal' => $lastbal ?: 0,
                    'status' => $bmstst,
                    'type' => $smsbodytype,
                    'trxid' => $trxid,
                    'mobile' => $number,
                    'sms_body' => $msg,
                    'simslot' => $dbinsertsim,
                    'deviceid' => $deviceid,
                    'telco' => $simoprt,
                    'commission' => $comm,
                    'sms_time' => $sms_date,
                    'smbal' => $courrentbmbalnumber,
                    'note' => $defranceamount,
                    'modem_id' => $modem_id,
                    'partner' => $partner_id ? $partner_id : '-1',
                    'dso' => $dso_id ? $dso_id : '-1',
                    'agent' => $user_id ? $user_id : '-1',
                    'merchent_id' => $merchant_code ? $merchant_code : '-1',
                    'ext_field' => $currentlastbal,
                    'ext_field_2' => $defran,
                    'idate' => $idate,
                    'token' => $token,
                    'ext_field_3' => $sms_date
                ]);

                if($bmanger){

         

                    $message = $msg;

                    $serviceRequestMethod = '';
                    $getSender = strtolower($bmanger->sender);

                    if($getSender == "16216"){
                        $serviceRequestMethod = 'rocket';
                    }elseif($getSender == "bkash"){
                        $serviceRequestMethod = 'bkash';
                    }elseif($getSender == "nagad" ){
                        $serviceRequestMethod = 'nagad';
                    }elseif($getSender == "upay" ){
                        $serviceRequestMethod = 'upay';
                    }

                    $service_requests = ServiceRequest::whereIn('status', [1, 6, 5])
                    ->where('mfs',$serviceRequestMethod)
                    ->get();
                        

                    foreach ($service_requests as $req_item) {
                        $number = (string) $req_item->number;



                        $encodedNumber = (strlen($number) >= 11)
                            ? substr($number, 0, 4) . "XXXX" . substr($number, 8)
                            : $number;

                                $amountString = (string) $req_item->amount;
                                if (empty($amountString) || (is_numeric($amountString) && (float)$amountString < 0)) {
                                    $amount = "0";
                                } else {
                                    $amount = strtok($amountString, '.'); // Get the integer part of the amount
                                    if (empty($amount)) {
                                        $amount = "0";
                                    }
                                }

                                $messageContainsNumber = strpos($message, $number) !== false ||
                                     strpos($message, $encodedNumber) !== false ||
                                     strpos($message, substr($encodedNumber, 1)) !== false ||
                                     strpos($message, substr($number, 1)) !== false;

                            $messageContainsSuccess = stripos($message, "success") !== false;

                            $normalizedMessage = normalizeAmount($message);
                            $normalizedAmount = normalizeAmount($amount);

                            $messageContainsAmount = stripos($normalizedMessage, $normalizedAmount) !== false;

                            if ($messageContainsNumber && $messageContainsSuccess && $messageContainsAmount) {
                                $req_item->status = 2;
                                // $req_item->result = $message;

                                // $words = explode(' ', $message);
                                // $longestWord = '';
                                // foreach ($words as $word) {
                                //     if (strlen($word) > strlen($longestWord)) {
                                //         $longestWord = $word;
                                //     }
                                // }

                                // $req_item->get_trxid = $longestWord;


                                $trxid = '';
                                    $isCashIn = stripos($message, 'Cash In') !== false || stripos($message, 'Cash-In') !== false;

                                    if ($isCashIn) {
                                        if ($serviceRequestMethod === 'bkash' || $serviceRequestMethod === 'upay') {
                                            // Cash In format: "TrxID CGD8MS316I"
                                            if (preg_match('/TrxID[\s:]*([A-Z0-9]+)/i', $message, $matches)) {
                                                $trxid = $matches[1];
                                            }

                                        } elseif ($serviceRequestMethod === 'nagad') {
                                            // Cash In format: "TxnID: 745I7GNM"
                                            if (preg_match('/TxnID[\s:]*([A-Z0-9]+)/i', $message, $matches)) {
                                                $trxid = $matches[1];
                                            }

                                        } elseif ($serviceRequestMethod === 'rocket') {
                                            // Rocket B2C format: "TxnId: 5562724147"
                                            if (preg_match('/TxnId[\s:]*([0-9]+)/i', $message, $matches)) {
                                                $trxid = $matches[1];
                                            }
                                        }
                                    }

                                    if ($trxid) {
                                        $req_item->get_trxid = $trxid;
                                        $req_item->action_by = 'Automatic';
                                        $req_item->save();

                                
                                    }

                                


                                sleep(5);

                                

                            

                                break; // Keep this if you want to stop after the first match
                            }


                    }

                }





            }
        }

        $statsusinsert = false;

        if ($smsinbxcrt) {
            DB::commit();
            $statsusinsert = true;
            $status_code = 200;
            $status = 'success';
        } else {
            DB::rollback();
            $statsusinsert = false;
            $status_code = 500;
            $status = 'error';
        }

        return response()->json([
            'message' => 'message 0' . $statsusinsert . ' success',
            'status_code' => $status_code,
            'status' => $status,
            'smstime' => $request_time,
            'success' => $statsusinsert
        ]);
    }


   public function verify_rn_device(Request $request)
{
    $membercode = $request->membercode ?: $request->opcode;
    $oparetor = $request->oparator;
    $oparetor2 = $request->oparator2;
    $simid = $request->simid;
    $simid2 = $request->simid2;
    $deviceid = $request->deviceid;
    $sim_number = $request->simnumber;
    $sim_number2 = $request->simnumber2;
    $modem_details = $request->modem_details;
    $token = Str::random(32);

    if (empty($membercode)) {
        return response()->json([
            'message' => 'Member id empty',
            'status_code' => 500,
            'success' => false,
        ]);
    }

    if (empty($deviceid)) {
        return response()->json([
            'message' => 'Device id empty',
            'status_code' => 500,
            'success' => false,
        ]);
    }

    if (empty($sim_number) && empty($sim_number2)) {
        return response()->json([
            'message' => 'Sim Number empty',
            'status_code' => 500,
            'success' => false,
        ]);
    }

    $userfound = User::where('user_type', 'agent')
        ->where('member_code', $membercode)
        ->where('status', 1)
        ->exists();

    if (!$userfound) {
        return response()->json([
            'message' => 'User id Not Found',
            'status_code' => 500,
            'success' => false,
        ]);
    }

    DB::beginTransaction();
    try {
        $modemId = null;
        $modemId2 = null;

        $modems = Modem::where('type', 'android')
            ->where('deviceid', $deviceid)
            ->where('member_code', $membercode)
            ->get();

        if ($modems->isEmpty()) {
            // Insert new modems
            if (!empty($sim_number)) {
                $modem = Modem::create([
                    'type' => 'android',
                    'member_code' => $membercode,
                    'deviceid' => $deviceid,
                    'operator' => $oparetor ?: null,
                    'sim_id' => $simid ?: $sim_number,
                    'sim_number' => $sim_number,
                    'modem_details' => $modem_details,
                    'token' => $token,
                    'up_time' => time(),
                    'status' => 1,
                    'simslot' => 0
                ]);
                $modemId = $modem->id;
            }
            if (!empty($sim_number2)) {
                $modem2 = Modem::create([
                    'type' => 'android',
                    'member_code' => $membercode,
                    'deviceid' => $deviceid,
                    'operator' => $oparetor2 ?: null,
                    'sim_id' => $simid2 ?: $sim_number2,
                    'sim_number' => $sim_number2,
                    'modem_details' => $modem_details,
                    'token' => $token,
                    'up_time' => time(),
                    'status' => 1,
                    'simslot' => 1
                ]);
                $modemId2 = $modem2->id;
            }
        } else {
            // Update existing modems
            if (count($modems) > 2) {
                // If there are more than 2 modems, delete the excess
                $modems->skip(2)->each->delete();
            }
            foreach ($modems as $index => $modem) {
                if ($modem->simslot == 0 && !empty($sim_number)) {
                    $modem->update([
                        'up_time' => time(),
                        'operator' => $oparetor,
                        'sim_number' => $sim_number,
                        'token' => $token,
                        'simslot' => 0
                    ]);
                    $modemId = $modem->id;
                }
                if ($modem->simslot == 1 && !empty($sim_number2)) {
                    $modem->update([
                        'up_time' => time(),
                        'operator' => $oparetor2,
                        'sim_number' => $sim_number2,
                        'token' => $token,
                        'simslot' => 1
                    ]);
                    $modemId2 = $modem->id;
                }
            }
        }

        DB::commit();

        return response()->json([
            'message' => 'Successful login modem',
            'username' => $membercode,
            'modemId' => $modemId,
            'modemId2' => $modemId2,
            'token' => $token,
            'status_code' => 200,
            'success' => true,
        ]);
    } catch (\Exception $e) {
        DB::rollback();
        return response()->json([
            'message' => 'Transaction failed: ' . $e->getMessage(),
            'status_code' => 500,
            'success' => false,
        ]);
    }
}


    public function update_rn_sms_app($current_version)
    {
        $latest_version_from_store = app_config('rn_sms_app_version');

        if ($current_version != $latest_version_from_store) {
            return response()->json([
                'message' => 'Your SMS App need to update for smooth work.',
                'status_code' => 200,
                'latest_version' => $latest_version_from_store,
                'update' => true
            ]);
        }

        return response()->json([
            'message' => 'Your SMS App Version Up to date.',
            'status_code' => 200,
            'latest_version' => $latest_version_from_store,
            'update' => false
        ]);
    }


}
