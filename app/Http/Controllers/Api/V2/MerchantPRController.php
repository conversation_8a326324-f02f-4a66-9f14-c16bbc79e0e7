<?php

namespace App\Http\Controllers\Api\V2;

use App\Helpers\BalanceManagerConstant;
use App\Http\Controllers\Controller;
use App\Models\Modem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Http\Requests\MakeTransactionRequest;
use App\Models\Merchant;
use App\Models\PaymentRequest;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class MerchantPRController extends Controller
{
    public function check()
    {
        return Auth::user();
    }

    public function mfsList()
    {
        return listOfOp();
    }

    public function makeTransaction(Request $request)
    {

       $possibleStatus =  responseV2();

        $validator = Validator::make(
            $request->all(),
            [
                'amount' => 'required|numeric',
                'reference' => ['required', 'string', 'min:3', 'max:20', Rule::unique('payment_requests', 'reference')->where('merchant_id', $request->merchant_id)],
                'currency' => ['required', Rule::in(['BDT'])],
                'callback_url' => 'required|url',
                'cust_name' => 'nullable|min:3|max:50',
                // 'cust_email' => 'required|email',
                'cust_phone' => 'nullable|min:3|max:15',
                'cust_address' => 'nullable|min:3|max:100',
                'checkout_items' => 'sometimes|array',
                'note' => 'sometimes|string',
                'transaction_id' => [
                    'required',
                    'string',
                    'min:8',
                    'max:10',
                    'regex:/^[a-zA-Z0-9]+$/',
                    function ($attribute, $value, $fail) {
                        // Check if transaction ID exists with status 1 or 2
                        $exists = PaymentRequest::where('payment_method_trx', $value)
                            ->whereIn('status', [1, 2, 0])
                            ->exists();

                        if ($exists) {
                            $fail('The transaction ID is already used & it is under working.');
                        }
                    },
                ],
                'from_number' => 'nullable',
                'payment_method' => 'required|in:bkash,nagad',
                'sim_number' => 'required|exists:modems,sim_id',
            ],
            [
                // Custom messages
                'amount.required' => 'The amount field is required.',
                'amount.numeric' => 'The amount must be a valid number.',
                'reference.required' => 'The reference field is required.',
                'reference.string' => 'The reference must be a string.',
                'reference.min' => 'The reference must be at least 3 characters long.',
                'reference.max' => 'The reference must not exceed 20 characters.',
                'reference.unique' => 'The reference has already been taken for this merchant.',
                'currency.required' => 'The currency field is required.',
                'currency.in' => 'The currency must be either BDT.',
                'callback_url.required' => 'The callback URL is required.',
                'callback_url.url' => 'The callback URL must be a valid URL.',
                'cust_name.required' => 'Customer name is required.',
                'cust_name.min' => 'Customer name must be at least 3 characters long.',
                'cust_name.max' => 'Customer name must not exceed 50 characters.',
                'cust_email.required' => 'Customer email is required.',
                'cust_email.email' => 'Customer email must be a valid email address.',
                'cust_phone.required' => 'Customer phone is required.',
                'cust_phone.min' => 'Customer phone must be at least 3 characters long.',
                'cust_phone.max' => 'Customer phone must not exceed 15 characters.',
                'cust_address.min' => 'Customer address must be at least 3 characters long.',
                'cust_address.max' => 'Customer address must not exceed 100 characters.',
                'checkout_items.array' => 'Checkout items must be an array.',
                'note.string' => 'The note must be a string.',
                'transaction_id.required' => 'Transaction number is required.',
                'transaction_id.string' => 'Transaction number must be a string.',
                'transaction_id.min' => 'Transaction number must be at least 9 characters long.',
                'transaction_id.max' => 'Transaction number must not exceed 10 characters.',
                'transaction_id.regex' => 'Transaction number must be alphanumeric.',
                'from_number.required' => 'From number is required.',
                'payment_method.required' => 'Payment method is required.',
                'payment_method.in' => 'Payment method must be either bkash or nagad.',
                'sim_number.required' => 'SIM number is required.',
                'sim_number.exists' => 'The SIM number does not exist in the modem records.',
            ],
        );

        // Check if validation fails
        if ($validator->fails()) {
            return response()->json(
                [
                    'errors' => $validator->errors(),
                    'status'=>'error',
                    // 'possible_status' => $possibleStatus,
                ],
                422,
            );
        }

        $agent = DB::table('modems')
            ->where('sim_number', $request->sim_number)
            ->first();

        if (!$agent) {
            return response()->json(
                [
                    'status'=>false,
                    'message' => 'Failed to find agent information. Contact with admin',
                ],
                400,
            );
        }

        // $exists = DB::table('balance_managers')
        //     ->where('trxid', $request->transaction_id)
        //     ->exists();

        // if (!$exists) {
        //     $checkAttempt = checkAttempt($request->transaction_id, $request->amount, $request->payment_method);

        //     if (!$checkAttempt) {
        //         return response()->json(['message' => 'Transaction ID not found, Please try again later', 'status' => false, 'possible_status' => $possibleStatus], 401);
        //     }
        // } else {
        //     $deleteAttempt = deleteAttempt($request->transaction_id, $request->amount, $request->payment_method);
        // }

        $balanceManagerQuery = DB::table('balance_managers')
            ->where('sim', $request->sim_number)
            ->where('trxid', $request->transaction_id)
            ->where('amount', $request->amount);

        if ($request->payment_method == 'bkash') {
            $balanceManagerQuery->where('type', 'bkcashout');
        } elseif ($request->payment_method == 'nagad') {
            $balanceManagerQuery->where('type', 'ngcashout');
        }

        $balanceManager = $balanceManagerQuery->first();

        if ($balanceManager->status == BalanceManagerConstant::SUCCESS || $balanceManager->status == BalanceManagerConstant::APPROVED) {
            $data['status'] = 1;
        } else {
            $data['status'] = 0;
        }

        $get_merchant_info = Merchant::find(Auth::user()->id);
        $getAdminMerchant = null;
        $merchant_type = 'general';

        if($get_merchant_info->merchant_type == 'sub_merchant'){
            $getAdminMerchant = $get_merchant_info->create_by;
            $merchant_type = 'sub_merchant';
        }

        $data['agent'] = $agent->member_code;
        $data['partner'] = getPartnerFromAgent($agent->member_code)->member_code;
        $data['from_number'] = $request->from_number;
        $data['modem_id'] = $agent->id;
        $data['request_id'] = generatePaymentRequestTrx(25);
        $data['payment_method_trx'] = $request->transaction_id;
        $data['sim_id'] = $request->sim_number;
        $data['trxid'] = generatePaymentRequestTrx(6);
        $data['amount'] = $request->amount;
        $data['payment_method'] = $request->payment_method;
        $data['merchant_id'] = $merchant_type=='sub_merchant'? $getAdminMerchant :  Auth::user()->id ;
        $data['sub_merchant'] =  $merchant_type=='sub_merchant'?  Auth::user()->id : null;
        $data['reference'] = $request->reference;
        $data['currency'] = $request->currency;
        $data['callback_url'] = rtrim($request->callback_url . '/');
        $data['cust_name'] = $request->cust_name;
        $data['cust_phone'] = $request->cust_phone;
        // $data['cust_email'] = $request->cust_email;
        $data['cust_address'] = $request->cust_address;
        $data['cust_address'] = $request->cust_address;
        if ($request->has('checkout_items')) {
            $data['checkout_items'] = json_encode($request->checkout_items);
        }
        if ($request->has('ext_field_1')) {
            $data['ext_field_1'] = json_encode($request->ext_field_1);
        }
        if ($request->has('ext_field_2')) {
            $data['ext_field_2'] = json_encode($request->ext_field_2);
        }

        $data['issue_time'] = Carbon::now();

        $data['ip'] = \request()?->ip();
        $data['user_agent'] = \request()?->userAgent();

        // $additionalData = [
        //     'callback_url' => $request->callback_url,
        //     'cust_name' => $request->cust_name,
        //     'cust_phone' => $request->cust_phone,
        //     'amount' => $request->amount,
        // ];

        // return $data['status'];

        $check = PaymentRequest::create($data);

        if ($check) {
            if ($data['status'] == 1) {
                return response()->json(
                    [
                        'status' => 'success',
                        'message' => 'Transaction is successful',
                    ],
                    200,
                );
            } else {
                return response()->json(
                    [
                        'status' => 'pending',
                        'message' => 'Transaction is processing',
                    ],
                    200,
                );
            }
        }

        return response()->json(
            [
                'status' => 'error',
                'message' => 'Please contact with admin',
            ],
            520,
        );
    }
}
