<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to your application's "home" route.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/dashboard';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        RateLimiter::for('mapi', function (Request $request) {
            return Limit::perMinute(600000)->by($request->user()?->id ?: $request->ip());
        });
        
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(600000)->by($request->user()?->id ?: $request->ip());
        });
        

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('api')
                ->prefix('ibot')
                ->group(base_path('routes/ibot.php'));


            Route::middleware('web')
                ->group(base_path('routes/web.php'));
				
			Route::middleware('web')
			//->namespace($this->namespace)
			->group(base_path('routes/admin.php'));
			
			Route::middleware('web')
			//->namespace($this->namespace)
			->group(base_path('routes/merchant.php'));

			Route::middleware('web')
			//->namespace($this->namespace)
			->group(base_path('routes/customer.php'));
        });
    }
}
